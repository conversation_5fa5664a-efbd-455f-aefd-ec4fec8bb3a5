# Lessons

- For website image paths, always use the correct relative path (e.g., 'images/filename.png') and ensure the images directory exists
- For search results, ensure proper handling of different character encodings (UTF-8) for international queries
- Add debug information to stderr while keeping the main output clean in stdout for better pipeline integration
- When using seaborn styles in matplotlib, use 'seaborn-v0_8' instead of 'seaborn' as the style name due to recent seaborn version changes
- When using Jest, a test suite can fail even if all individual tests pass, typically due to issues in suite-level setup code or lifecycle hooks
- When using Next.js with `next/font` and a custom Babel config, you need to explicitly enable SWC in next.config.js/ts with `experimental: { forceSwcTransforms: true }`
- To fix hydration errors in Next.js when browser extensions modify HTML attributes, use the `suppressHydrationWarning` attribute on the affected element (usually the `html` tag)
- When using React Hook Form with controlled inputs, always provide a defined default value (e.g., use 0 instead of undefined for number inputs) to avoid React warnings about switching between controlled and uncontrolled inputs
- When implementing AI-based features, always add robust error handling and fallback mechanisms to ensure the application works even when the AI service fails
- When working with Supabase storage, use the dashboard to create buckets and set policies rather than trying to do it via SQL or API calls, as the storage system is separate from the database
- When using Supabase with foreign key constraints, ensure that records exist in the referenced tables before inserting new records. For example, when using Supabase auth with a users table that has foreign key relationships, make sure to create corresponding records in the users table for authenticated users
- When working with date fields from Supabase in Laravel Blade templates, always check if the date is a string or a Carbon object before calling format() to avoid "Call to a member function format() on string" errors
- When using Supabase for user management, the service role key is required for admin operations like creating users. The anon key has limited permissions and can't create users directly without email verification.
- Need to use Supabase service role key for admin operations like creating users
- Add the service role key to the .env file: `SUPABASE_SERVICE_ROLE=your_service_role_key_here`
- Configure the service role key in config/supabase.php
- When using only Supabase for authentication (no Laravel Auth):
  - Store Supabase token in session instead of creating Laravel users
  - Use a custom middleware (SupabaseAuth) to verify Supabase tokens
  - Update routes to use the Supabase middleware instead of Laravel's auth middleware
  - Handle logout by signing out from Supabase and clearing the session
- For date handling in JavaScript, always validate dates with isNaN(date.getTime()) to check if a date is valid before using it in calculations or comparisons
- When removing Vite from a Laravel project, load Tailwind CSS directly from a CDN and create custom CSS files in the public directory

## Windsurf learned

- For search results, ensure proper handling of different character encodings (UTF-8) for international queries
- Add debug information to stderr while keeping the main output clean in stdout for better pipeline integration
- When using seaborn styles in matplotlib, use 'seaborn-v0_8' instead of 'seaborn' as the style name due to recent seaborn version changes
- Use 'gpt-4o' as the model name for OpenAI's GPT-4 with vision capabilities 

# Scratchpad

## Current Session: Replace View Menu Button with Search Box

### Task
Remove the View Menu button and replace it with a search box for food items:
- [x] Examine current structure to locate View Menu button
- [x] Design search box component with appropriate styling
- [x] Replace View Menu button with search box
- [x] Add search functionality for food items
- [x] Ensure responsive design for mobile and desktop
- [x] Update test file to reflect changes
- [x] Test search functionality (user confirmed working)
- [x] Create new branch for changes
- [x] Commit all changes

### Commit Details
- Branch: feature/replace-view-menu-with-search
- Commit hash: 1a7a753
- Message: "feat: Replace View Menu button with search box for food items"
- Files changed: 3 (HeroSection.tsx, HeroSection.test.tsx, scratchpad.md)
- Lines: 80 insertions, 35 deletions
- Remote URL: https://github.com/shadiqaddoura/Firefly-Burger-Web/pull/new/feature/replace-view-menu-with-search

✅ **Task Completed Successfully**

## Current Session: Add CTA and Margin to Search Box

### Task
Add improvements to the search box area:
- [x] Add a new CTA (Call-to-Action) above the search box
- [x] Add bottom margin under the search box
- [x] Ensure proper spacing and visual hierarchy
- [x] Update test file to include new CTA text
- [x] Test the updated layout
- [x] Commit changes

### Commit Details
- Branch: feature/replace-view-menu-with-search
- Commit hash: b6bb902
- Message: "feat: Add CTA above search box and bottom margin"
- Files changed: 3 (HeroSection.tsx, HeroSection.test.tsx, scratchpad.md)
- Lines: 49 insertions, 4 deletions

✅ **Task Completed Successfully**

## Current Session: Create Dashboard Template with Authentication

### Task
Create a comprehensive dashboard template with sign-in and sign-up pages for managing websites and orders:
- [x] Examine current project structure and dependencies
- [x] Plan dashboard architecture and authentication flow
- [x] Create authentication pages (Sign In, Sign Up, Password Reset)
- [x] Create dashboard layout with sidebar navigation
- [x] Create website management pages
- [x] Create order management pages
- [x] Add routing for all dashboard pages
- [x] Implement responsive design
- [x] Add demo data and mock functionality
- [x] Test all dashboard functionality
- [x] Create new branch for changes
- [x] Commit all changes

### Commit Details
- Branch: feature/dashboard-template
- Commit hash: 8d78b33
- Message: "feat: Add comprehensive dashboard template with authentication"
- Files changed: 12 (10 new files, 2 modified)
- Lines: 3,238 insertions, 19 deletions

✅ **Task Completed Successfully**

## Current Session: Replace Websites with Menu Management

### Task
Remove the websites section and replace it with menu management for a single website:
- [x] Remove websites-related pages and components
- [x] Update dashboard layout navigation to replace websites with menu
- [x] Create menu management pages (Menu Items, Categories, Add Item)
- [x] Update dashboard overview to show menu stats instead of website stats
- [x] Update routing to replace website routes with menu routes
- [x] Add demo menu data and functionality
- [x] Test all menu management functionality
- [x] Commit all changes

### Commit Details
- Branch: feature/dashboard-template
- Commit hash: d0e8b23
- Message: "feat: Replace websites section with menu management system"
- Files changed: 9 (3 new files, 2 deleted, 4 modified)
- Lines: 1,311 insertions, 693 deletions

✅ **Task Completed Successfully**

## Current Session: Documentation Review and Gap Analysis

### Task
Review the customer website and identify what's missing from the project documentation:
- [x] Examine the live website at localhost:8081
- [x] Compare actual implementation with documented features
- [x] Analyze project structure and routing
- [x] Check for missing components and pages
- [x] Review Firebase integration status
- [x] Identify mobile app implementation gaps
- [x] Document findings and recommendations

### Analysis Results

**✅ IMPLEMENTED FEATURES:**
- Customer landing page with hero, search, categories, featured burgers
- Corporate website page
- Dashboard system with authentication (sign in/up/reset)
- Menu management system (items, categories, add new)
- Order management and analytics dashboard
- Responsive design with Tailwind CSS
- shadcn/ui component library integration

**❌ MISSING CUSTOMER FEATURES (Documented but Not Implemented):**
- Item detail pages (/items/:id)
- Checkout system (/checkout, /fast-checkout)
- Address management (/addresses)
- Branch locator (/branches)
- International branch selector (/branch-selector)
- Subscription plans (/subscription/*)
- Rewards system (/rewards)
- Customer-specific components (BranchSelector, SubscriptionPlans, RewardsSection)

**❌ MISSING CORPORATE FEATURES:**
- About page (/corporate/about)
- Franchise opportunities (/corporate/franchise)
- Franchise application wizard (/corporate/franchise/apply)
- Investor relations (/corporate/investors)
- Careers page (/corporate/careers)
- Press page (/corporate/press)
- Corporate-specific components (CorporateHero, FranchiseOpportunities, etc.)

**❌ MISSING BACKEND INTEGRATION:**
- No Firebase configuration files (firebase.json, .firebaserc)
- No Firebase SDK dependencies in package.json
- No environment variables for Firebase config
- No authentication integration with Firebase Auth
- No real-time database integration with Firestore
- No cloud functions implementation
- No payment integration with Stripe

**❌ MISSING MOBILE APP:**
- No mobile/ directory or React Native implementation
- No mobile-specific dependencies
- No app store assets or configuration
- No push notification setup
- No biometric authentication
- No mobile wallet integration

**❌ MISSING TECHNICAL INFRASTRUCTURE:**
- No state management beyond React Query (no Zustand)
- No internationalization setup
- No currency conversion system
- No geolocation services
- No real-time features implementation
- No offline support
- No PWA configuration

**📊 DOCUMENTATION ACCURACY:**
- Documentation is overly ambitious compared to actual implementation
- Many features are documented as "implemented" but are actually "planned"
- Project structure in docs doesn't match actual file structure
- Tech stack includes many dependencies not actually installed

### Additional Update
- [x] Reduce padding inside category cards (p-6 → p-4)
- [x] Commit padding reduction

### Commit Details
- Branch: feature/landing-page-updates
- Commit hash: c0b0ba6
- Message: "feat: Reduce padding inside category cards"
- Files changed: 2 (BurgerCategories.tsx, scratchpad.md)
- Lines: 15 insertions, 3 deletions

✅ **Padding Reduction Update Completed Successfully**

### Image Size and Spacing Update
- [x] Make category images bigger (w-32 h-32 → w-40 h-40)
- [x] Reduce bottom margin between image and title (mb-2 → mb-1)
- [x] Commit image size and spacing changes

### Commit Details
- Branch: feature/landing-page-updates
- Commit hash: b890d98
- Message: "feat: Improve category card image size and spacing"
- Files changed: 2 (BurgerCategories.tsx, scratchpad.md)
- Lines: 17 insertions, 3 deletions

✅ **Image Size and Spacing Update Completed Successfully**

### Slider Layout Update
- [x] Import Carousel components from ui/carousel
- [x] Replace grid layout with horizontal carousel
- [x] Set fixed width for category cards (w-[280px])
- [x] Add carousel navigation buttons (Previous/Next)
- [x] Configure carousel options (align: start, loop: false)
- [x] Preserve all existing functionality and animations
- [x] Commit slider layout changes

### Commit Details
- Branch: feature/landing-page-updates
- Commit hash: 2abe1a3
- Message: "feat: Convert categories to horizontal slider layout"
- Files changed: 2 (BurgerCategories.tsx, scratchpad.md)
- Lines: 53 insertions, 14 deletions

✅ **Horizontal Slider Layout Update Completed Successfully**

### Carousel Spacing Update
- [x] Add bottom margin to carousel container (mb-8)
- [x] Add padding to CarouselContent (p-2)
- [x] Commit spacing improvements

### Commit Details
- Branch: feature/landing-page-updates
- Commit hash: 1697e2d
- Message: "feat: Improve carousel spacing and padding"
- Files changed: 2 (BurgerCategories.tsx, scratchpad.md)
- Lines: 16 insertions, 4 deletions

✅ **Carousel Spacing Update Completed Successfully**

### Remove Category Click Functionality
- [x] Remove Sheet imports (Sheet, SheetContent, SheetHeader, SheetTitle, SheetDescription)
- [x] Remove Star and Clock imports (no longer needed for menu items)
- [x] Remove MenuItem and CategoryDetails type definitions
- [x] Remove onClick prop from CategoryItemProps
- [x] Remove onClick handler from CategoryItem component
- [x] Remove cursor-pointer class from category cards
- [x] Remove all categoryDetails data and menu items
- [x] Remove selectedCategory and isSheetOpen state variables
- [x] Remove handleCategoryClick function
- [x] Remove entire Sheet component and menu items display
- [x] Clean up component to only show category cards without click functionality
- [x] Commit removal of click functionality

### Commit Details
- Branch: feature/landing-page-updates
- Commit hash: b0a10d6
- Message: "feat: Remove category click functionality and simplify component"
- Files changed: 2 (BurgerCategories.tsx, scratchpad.md)
- Lines: 28 insertions, 431 deletions

✅ **Category Click Functionality Removal Completed Successfully**

### Featured Burgers Styling Update
- [x] Remove 3D card imports (CardContainer, CardBody, CardItem)
- [x] Change section background from red gradient to white
- [x] Update card background from red gradient to white
- [x] Add subtle shadow to burger cards (shadow-md with hover:shadow-lg)
- [x] Remove 3D effects and translateZ properties
- [x] Update text colors for white background contrast:
  - [x] Title: text-gray-900 (dark text)
  - [x] Price: text-firefly-red (brand color)
  - [x] Description: text-gray-600 (medium gray)
  - [x] Section title: text-firefly-black with red accent
- [x] Update loading spinner colors for white background
- [x] Update plus button to solid firefly-red background
- [x] Preserve all functionality (animations, plus button, badges)
- [x] Commit featured burgers styling changes

### Commit Details
- Branch: feature/landing-page-updates
- Commit hash: 9073844
- Message: "feat: Update Featured Burgers to white theme with clean design"
- Files changed: 2 (FeaturedBurgers.tsx, scratchpad.md)
- Lines: 75 insertions, 58 deletions

✅ **Featured Burgers White Theme Update Completed Successfully**

### Grid Layout Update
- [x] Change grid from 3 columns to 4 columns per row
- [x] Update responsive breakpoints (sm:grid-cols-2 lg:grid-cols-4)
- [x] Make cards smaller and add spacing:
  - [x] Reduce card height from 450px to 350px
  - [x] Reduce image height from 220px to 180px
  - [x] Reduce padding from p-6 to p-4
  - [x] Reduce title font size from text-2xl to text-lg
  - [x] Reduce price font size from text-xl to text-lg
  - [x] Reduce description font size from text-sm to text-xs
  - [x] Make plus button smaller (w-12 h-12 instead of w-16 h-16)
  - [x] Reduce badge padding and positioning
  - [x] Increase gap back to gap-8 for better spacing between cards
- [x] Commit card size and spacing changes

### Commit Details
- Branch: feature/landing-page-updates
- Commit hash: d3fdc18
- Message: "feat: Optimize Featured Burgers layout with 4 columns and smaller cards"
- Files changed: 2 (FeaturedBurgers.tsx, scratchpad.md)
- Lines: 40 insertions, 16 deletions

✅ **Featured Burgers 4-Column Layout Optimization Completed Successfully**

### Project Documentation Creation
- [x] Review entire project structure and architecture
- [x] Analyze tech stack, dependencies, and configuration
- [x] Document recent landing page updates and optimizations
- [x] Create comprehensive PROJECT_DOCUMENTATION.md file including:
  - [x] Project overview and tech stack
  - [x] Architecture and routing structure
  - [x] Design system and brand colors
  - [x] Key features and components
  - [x] Recent updates and improvements
  - [x] Development setup and deployment
  - [x] Performance optimizations
  - [x] UI/UX design principles
  - [x] Contributing guidelines
- [x] Commit documentation file

### Commit Details
- Branch: feature/landing-page-updates
- Commit hash: af4b98c
- Message: "docs: Add comprehensive project documentation"
- Files changed: 2 (PROJECT_DOCUMENTATION.md created, scratchpad.md updated)
- Lines: 303 insertions, 1 deletion

✅ **Project Documentation Creation Completed Successfully**

### Documentation Updates for Customer Website Focus
- [x] Update project overview to focus on customer website
- [x] Add new routing structure with customer-focused pages:
  - [x] /items/:id (Item detailed page)
  - [x] /checkout (Checkout page)
  - [x] /fast-checkout (Fast checkout page)
  - [x] /addresses (Customer addresses)
  - [x] /branches (Find branch page)
- [x] Update key features section to reflect customer website:
  - [x] Change "Featured Burgers" to "Trending Items"
  - [x] Remove Instagram Feed reference
  - [x] Add Item Detailed Page features
  - [x] Add Checkout System features
  - [x] Add Customer Addresses features
  - [x] Add Find Branch Page features
  - [x] Add Footer with app download links
- [x] Add "Upcoming Features" section with detailed implementation plans
- [x] Update recent updates section to reflect trending items
- [x] Commit documentation updates

### Commit Details
- Branch: feature/landing-page-updates
- Commit hash: 7904059
- Message: "docs: Update documentation for customer website focus"
- Files changed: 2 (PROJECT_DOCUMENTATION.md, scratchpad.md)
- Lines: 124 insertions, 11 deletions

✅ **Customer Website Documentation Updates Completed Successfully**

### Corporate Website Documentation Addition
- [x] Add comprehensive Corporate Website section to project documentation
- [x] Update project overview to include both customer and corporate platforms
- [x] Add corporate-specific project structure and components
- [x] Include corporate brand colors (firefly-gold for premium/corporate)
- [x] Add corporate routing structure with franchise application wizard
- [x] Detail corporate website key features:
  - [x] Corporate Hero Section with franchise CTA
  - [x] Company Overview and brand story
  - [x] Franchise Opportunities with investment details
  - [x] Franchise Application Wizard (multi-step process)
  - [x] Company Statistics and performance metrics
  - [x] Franchisee Testimonials and success stories
  - [x] Investment Information and ROI projections
  - [x] Support System and training programs
  - [x] Corporate Footer with investor relations
- [x] Add upcoming corporate features:
  - [x] Interactive Territory Map
  - [x] Franchisee Portal
  - [x] Investment Calculator
  - [x] CRM Integration
- [x] Include Talabat-inspired franchise application flow
- [x] Update design principles for professional corporate audience
- [x] Commit comprehensive corporate website documentation

### Commit Details
- Branch: main
- Commit hash: 60be632
- Message: "docs: Add comprehensive Corporate Website documentation"
- Files changed: 2 (PROJECT_DOCUMENTATION.md, scratchpad.md)
- Lines: 511 insertions, 1 deletion

✅ **Corporate Website Documentation Addition Completed Successfully**

### International Branch System Documentation Addition
- [x] Add International Branch Selector to Customer Website features
- [x] Update routing structure with /branch-selector page
- [x] Add BranchSelector component to project structure
- [x] Update Hero Section to include international branch selector in header
- [x] Add comprehensive International Branch Selector features:
  - [x] Country and branch selection dropdown
  - [x] Real-time menu and pricing updates
  - [x] Currency conversion and localization
  - [x] Geolocation-based branch suggestions
  - [x] Branch availability and service hours
  - [x] Delivery zone validation for selected branch
- [x] Update Footer to show current branch and country indicator
- [x] Add International Branch System to upcoming features:
  - [x] Country selection with flag icons
  - [x] Branch listing with details and services
  - [x] Real-time menu synchronization
  - [x] Currency conversion and pricing updates
  - [x] Localized content and language support
  - [x] Branch-specific promotions and offers
- [x] Update technical improvements for international support:
  - [x] Multi-currency payment integration
  - [x] Internationalization and localization
  - [x] Currency management with real-time exchange rates
  - [x] Branch context global state management
  - [x] Analytics with branch segmentation
  - [x] SEO optimization with geo-targeting
- [x] Commit international branch system documentation

### Commit Details
- Branch: main
- Commit hash: 8135d73
- Message: "docs: Add International Branch System for global expansion"
- Files changed: 2 (PROJECT_DOCUMENTATION.md, scratchpad.md)
- Lines: 76 insertions, 13 deletions

✅ **International Branch System Documentation Completed Successfully**

### Project Goals Section Addition
- [x] Add comprehensive "Project Goals" section to documentation
- [x] Define Customer Website goals:
  - [x] Modern ordering platform with real-time pricing
  - [x] Seamless checkout and address management
  - [x] International branch switching with localization
  - [x] Order tracking and customer support
- [x] Define Corporate Website goals:
  - [x] Professional franchise platform
  - [x] Investment opportunities and ROI projections
  - [x] Franchise application wizard
  - [x] Franchisee success stories and territory analysis
- [x] Define Global Expansion goals:
  - [x] Multi-country branch management
  - [x] Currency conversion and localized pricing
  - [x] Multi-language support and localization
  - [x] International payment processing
  - [x] Branch-specific promotions and adaptations
- [x] Define Technical Infrastructure goals:
  - [x] High-performance dual-platform system
  - [x] Responsive design across all devices
  - [x] Advanced state management
  - [x] Comprehensive analytics and reporting
  - [x] SEO optimization and accessibility compliance
- [x] Commit project goals documentation

### Commit Details
- Branch: main
- Commit hash: d6cda1c
- Message: "docs: Add comprehensive Project Goals section"
- Files changed: 2 (PROJECT_DOCUMENTATION.md, scratchpad.md)
- Lines: 70 insertions, 1 deletion

✅ **Project Goals Documentation Addition Completed Successfully**

### Subscription Plans Feature Addition (Juicy & Fluffy)
- [x] Add subscription plans to Customer Website goals
- [x] Update routing structure with subscription-related pages:
  - [x] /subscription (Plans overview)
  - [x] /subscription/juicy (Juicy plan details)
  - [x] /subscription/fluffy (Fluffy plan details)
  - [x] /subscription/manage (Manage subscription)
  - [x] /rewards (Rewards and points system)
- [x] Add subscription components to project structure:
  - [x] SubscriptionPlans.tsx (component and page)
  - [x] RewardsSection.tsx (component)
  - [x] Subscription.tsx, ManageSubscription.tsx, Rewards.tsx (pages)
- [x] Define comprehensive subscription plan features:
  - [x] Juicy Plan benefits:
    * Exclusive menu items and early access
    * 2x reward points on all orders
    * Free delivery on orders over $15
    * Monthly surprise treats and discounts
    * Priority customer support
  - [x] Fluffy Plan benefits:
    * All Juicy plan benefits included
    * 3x reward points on all orders
    * Free delivery on all orders (no minimum)
    * Weekly exclusive offers and flash sales
    * VIP access to special events and tastings
    * Personalized meal recommendations
- [x] Add comprehensive rewards system:
  - [x] Points earning on every purchase
  - [x] Redemption catalog for free items and discounts
  - [x] Tier-based rewards (Bronze, Silver, Gold, Platinum)
  - [x] Birthday and anniversary bonuses
  - [x] Referral program with bonus points
  - [x] Integration with subscription plans for multiplied earnings
- [x] Update upcoming features with subscription management system
- [x] Update technical improvements for subscription and rewards integration
- [x] Update footer to include subscription status and rewards balance
- [x] Commit subscription plans and rewards system documentation

### Commit Details
- Branch: main
- Commit hash: 3cc849c
- Message: "docs: Add subscription plans and rewards system (Juicy & Fluffy)"
- Files changed: 2 (PROJECT_DOCUMENTATION.md, scratchpad.md)
- Lines: 121 insertions, 9 deletions

✅ **Subscription Plans and Rewards System Documentation Completed Successfully**

### Firebase Backend Infrastructure Documentation Addition
- [x] Update Technical Infrastructure goals to include Firebase BaaS
- [x] Add Firebase to key dependencies with version and services
- [x] Add comprehensive Firebase Backend Infrastructure section:
  - [x] Explain why Firebase is the optimal choice:
    * Scalability & Performance (auto-scaling, global CDN, real-time DB)
    * Cost Efficiency (pay-as-you-scale, serverless, reduced DevOps)
    * Security & Compliance (enterprise-grade, built-in auth, GDPR)
    * Development Speed (rapid prototyping, real-time features, easy integration)
  - [x] Detail Firebase Services Implementation:
    * Firebase Authentication (user management, social login, MFA, RBAC)
    * Cloud Firestore (menu, orders, users, branches, subscriptions, rewards)
    * Cloud Functions (order processing, payments, rewards, notifications)
    * Cloud Storage (images, uploads, marketing assets, branch media)
    * Firebase Analytics (user behavior, performance, A/B testing)
    * Cloud Messaging (order updates, promotions, franchise comms)
- [x] Update Component Architecture to include Firebase integration
- [x] Comprehensive Technical Improvements update:
  - [x] Firebase backend integration with all services
  - [x] Real-time database with Firestore
  - [x] Firebase Auth with social login
  - [x] Cloud Functions for serverless business logic
  - [x] Firebase Analytics with custom events
  - [x] FCM for push notifications
  - [x] Firebase Security Rules for data protection
  - [x] Performance Monitoring for optimization
- [x] Update Deployment section with Firebase Hosting:
  - [x] Frontend deployment with Firebase Hosting
  - [x] Backend deployment with Cloud Functions
  - [x] Environment configuration (dev, staging, production)
  - [x] Multi-region deployment for international branches
  - [x] Firebase project structure and configuration
  - [x] Advantages of Firebase deployment (CDN, SSL, atomic deployments)
- [x] Commit Firebase backend infrastructure documentation

### Commit Details
- Branch: main
- Commit hash: d0d0434
- Message: "docs: Add comprehensive Firebase Backend Infrastructure"
- Files changed: 2 (PROJECT_DOCUMENTATION.md, scratchpad.md)
- Lines: 191 insertions, 20 deletions

✅ **Firebase Backend Infrastructure Documentation Completed Successfully**

### Customer Mobile App Documentation Addition
- [x] Update project overview to include Customer Mobile App as third platform
- [x] Update project description to mention React Native for mobile
- [x] Update Customer Website goals to include mobile app exclusive features:
  - [x] Push notifications for order updates and promotions
  - [x] Offline menu browsing and order preparation
  - [x] Biometric authentication (Face ID, Touch ID)
  - [x] Location-based branch suggestions and auto-switching
  - [x] Mobile wallet integration (Apple Pay, Google Pay)
  - [x] Camera-based QR code scanning for quick reorders
- [x] Update Technical Infrastructure to include multi-platform system
- [x] Add mobile technologies to tech stack:
  - [x] React Native 0.73.x framework
  - [x] React Navigation 6.x for navigation
  - [x] Redux Toolkit + RTK Query for state management
  - [x] React Native Elements + Custom Components for UI
  - [x] StyleSheet + React Native Reanimated for styling
- [x] Add comprehensive mobile dependencies:
  - [x] Navigation, icons, animations, push notifications
  - [x] Biometric auth, camera, maps, payments
- [x] Add comprehensive Customer Mobile App features section:
  - [x] Native mobile experience with offline functionality
  - [x] Enhanced authentication with biometric login
  - [x] Push notifications for orders, promotions, rewards
  - [x] Location-based features with auto branch detection
  - [x] Mobile-optimized ordering with quick reorder and QR scanning
  - [x] Mobile wallet integration (Apple Pay, Google Pay)
  - [x] Camera features for QR codes and AR preview
  - [x] Offline capabilities with menu caching
  - [x] Mobile-specific UI/UX with gestures and haptic feedback
  - [x] App Store optimization for both iOS and Android
- [x] Update project structure to include mobile app architecture
- [x] Add mobile app development to upcoming features:
  - [x] iOS and Android application development
  - [x] Platform-specific features and optimizations
  - [x] Cross-platform code sharing and unified design
  - [x] App store submission and beta testing
- [x] Commit customer mobile app documentation

### Commit Details
- Branch: main
- Commit hash: ff02f77
- Message: "docs: Add comprehensive Customer Mobile App documentation"
- Files changed: 2 (PROJECT_DOCUMENTATION.md, scratchpad.md)
- Lines: 190 insertions, 10 deletions

✅ **Customer Mobile App Documentation Completed Successfully**

### Pitch Deck Content Creation
- [x] Create comprehensive PITCH_DECK_CONTENT.md file
- [x] Structure 14 slides for customer presentation
- [x] Include executive summary and project goals
- [x] Detail three-platform overview (Web + Mobile + Corporate)
- [x] Comprehensive customer platform features:
  - [x] Core ordering experience with smart search
  - [x] Subscription plans (Juicy & Fluffy) with benefits
  - [x] Loyalty & rewards system with tier progression
  - [x] Global features with branch switching and localization
- [x] Mobile app exclusive features:
  - [x] Native mobile experience with biometric auth
  - [x] Mobile payments and QR code scanning
  - [x] Camera features and AR preview
  - [x] Smart features with voice search and gestures
- [x] Corporate franchise platform features:
  - [x] Professional presentation and investment opportunities
  - [x] Franchise application system with guided wizard
  - [x] Business intelligence and market analysis
- [x] Technology stack overview:
  - [x] Frontend technologies (React, React Native, TypeScript)
  - [x] Firebase backend infrastructure and services
  - [x] Payment integration and global infrastructure
- [x] Firebase value proposition with scalability and cost benefits
- [x] Business value proposition with revenue streams
- [x] Market impact for customers, franchises, and industry
- [x] Implementation roadmap with 4 phases
- [x] Success metrics and target achievements
- [x] Call to action with next steps and contact information
- [x] Update contact information to Skyline Innovation team details
- [x] Commit pitch deck content file

### Commit Details
- Branch: main
- Commit hash: 372c0b0
- Message: "docs: Add comprehensive pitch deck content and update contact information"
- Files changed: 2 (PITCH_DECK_CONTENT.md created, scratchpad.md updated)
- Lines: 367 insertions

✅ **Pitch Deck Content Creation Completed Successfully**

### Implementation Details
**Desktop Navigation:**
- Added Sign Up button to top-right navigation area
- Styled with Firefly red background (bg-firefly-red)
- Hover effects with darker red (hover:bg-red-700)
- Shadow effects for depth (shadow-lg hover:shadow-xl)
- Proper spacing with existing navigation items

**Mobile Navigation:**
- Added Sign Up button to mobile menu overlay
- Larger size for touch interaction (text-2xl, px-8 py-3)
- Consistent styling with desktop version
- Closes mobile menu when clicked

**Styling Features:**
- Uses Firefly brand red color (#f8371a)
- Smooth transitions (transition-all duration-300)
- Rounded corners (rounded-md)
- White text for contrast
- Responsive sizing for different screen sizes

### Menu Management Pages to Create
1. **Menu Overview** (/dashboard/menu)
   - List all menu items with categories
   - Search and filter functionality
   - Quick actions (add item, manage categories)

2. **Add Menu Item** (/dashboard/menu/new)
   - Form to add new menu items
   - Category selection, pricing, descriptions
   - Image upload for menu items

3. **Menu Categories** (/dashboard/menu/categories)
   - Manage menu categories
   - Add, edit, delete categories
   - Reorder categories

### Dashboard Pages to Create
1. **Authentication Pages**
   - Sign In page (/auth/signin)
   - Sign Up page (/auth/signup)
   - Password reset page (/auth/reset)

2. **Dashboard Layout**
   - Main dashboard layout with sidebar
   - Header with user profile and notifications
   - Responsive sidebar navigation

3. **Website Management**
   - Websites overview (/dashboard/websites)
   - Website details/edit (/dashboard/websites/:id)
   - Add new website (/dashboard/websites/new)

4. **Order Management**
   - Orders overview (/dashboard/orders)
   - Order details (/dashboard/orders/:id)
   - Order analytics (/dashboard/analytics)

5. **User Management**
   - Profile settings (/dashboard/profile)
   - Account settings (/dashboard/settings)

### Technical Implementation Plan
- Use React Router for navigation
- Implement protected routes for dashboard
- Use shadcn/ui components for consistent design
- Create reusable dashboard layout component
- Add form validation with react-hook-form and zod
- Use Firefly brand colors and styling
- Add responsive design for mobile and desktop
- Include demo data and mock API calls

### Implementation Details
**Corporate Page Sections Created:**
1. **Hero Section** - Full-screen banner with Firefly Burger branding and corporate messaging
2. **About Section** - Company story, founding details, and growth narrative with burger image
3. **Mission & Vision** - Side-by-side cards explaining company purpose and future goals
4. **Core Values** - Three-column layout showcasing Quality, Innovation, and Community values
5. **Leadership Team** - Team member cards with placeholder profiles (CEO, Head Chef, COO)
6. **Contact Section** - Corporate contact information and call-to-action button

**Technical Implementation:**
- Created `/src/pages/Corporate.tsx` with full responsive design
- Added `/corporate` route to `App.tsx` routing configuration
- Updated `Navbar.tsx` to use React Router `Link` instead of hash navigation
- Maintained consistent Firefly brand styling (red gradient backgrounds, cream text, etc.)
- Used existing burger images and brand colors from tailwind config
- Implemented responsive grid layouts for mobile and desktop
- Added hover effects and transitions consistent with existing components

**Demo Content Added:**
- Company founding story (2015 establishment)
- Mission statement focused on quality and community
- Vision for global burger brand recognition
- Core values with detailed descriptions
- Leadership team with placeholder names and roles
- Corporate contact information with demo addresses and phone numbers

### Analysis
✅ **Current Structure Analyzed:**
- **Routing**: React Router setup in App.tsx with Index and NotFound pages
- **Navigation**: Corporate link currently uses hash (#corporate) in Navbar.tsx
- **Styling**: Firefly brand colors (red: #f8371a, black: #121212, cream: #FFF8E1)
- **Components**: Follow pattern with Navbar + content + Footer structure
- **Images**: Stored in /public directory, referenced with relative paths

**Corporate Page Requirements:**
- Create new Corporate.tsx page component
- Add route to App.tsx (/corporate)
- Update Navbar links from hash to React Router Link
- Include sections: Hero, About, Mission, Values, Team, etc.
- Use consistent styling with existing components
- Add demo content and placeholder images

### Implementation Details
**New SearchSection Component:**
- Created standalone component with white background (bg-white)
- Moved CTA content: "Find Your Perfect Burger" heading and subheading
- Redesigned search input with:
  - Light theme styling (gray borders, black text)
  - Larger size and better spacing (py-5, px-16)
  - Focus states with firefly-red accent color
  - Shadow effects for depth (shadow-lg hover:shadow-xl)
  - Maximum width of 2xl for better proportions

**HeroSection Updates:**
- Removed all search-related imports and state
- Removed CTA and search form elements
- Simplified to focus on hero messaging only
- Maintained existing styling and burger image

**Page Structure:**
- Added SearchSection import to Index page
- Positioned between HeroSection and BurgerCategories
- Creates logical flow: Hero → Search → Categories

**Test Updates:**
- Updated HeroSection test to remove search-related assertions
- Created new SearchSection test file with comprehensive coverage
- Maintains test coverage for all functionality

### Implementation Details
- Added CTA section above search box with:
  - Main heading: "Find Your Perfect Burger" (text-2xl md:text-3xl font-bold)
  - Subheading: "Search our menu and discover your next favorite meal" (text-lg)
  - Centered text alignment with proper spacing (mb-6)
- Added bottom margin to search box container (mb-12)
- Maintained consistent white text styling with opacity variations
- Updated test file to verify new CTA text elements
- Improved visual hierarchy with proper spacing between elements

### Analysis
Found View Menu button in HeroSection.tsx:
- Located on line 60 in the call-to-action area
- Currently styled as outline button with white border and text
- Positioned in flex container with gap-4 and centered
- Need to replace with search input that matches the design aesthetic

### Implementation Details
- Added useState hook for search query state management
- Imported Search icon from lucide-react
- Created form with onSubmit handler for search functionality
- Designed search input with:
  - Transparent background with white border
  - Search icon positioned on the left
  - White text and placeholder styling
  - Focus states with ring and border effects
  - Responsive design with max-width constraint
- Updated test file to check for search input instead of View Menu button
- Maintained consistent styling with the hero section's white/transparent theme

### Previous Task Completed ✅
Navigation menu update:
- Replaced existing menu items with 'Corporate' and 'Become a Partner'
- Positioned new items in top right
- Commit: 0ba5a4c

### Current Navigation Analysis
The Navbar component currently has:
- Logo centered at top
- Desktop nav: Home, Menu, Locations, Contact, Order Online button, Cart icon (all centered)
- Mobile nav: Same items in overlay menu
- Need to replace all menu items with just Corporate and Become a Partner in top right

### Analysis
**Current FEATURED BURGERS styling:**
- Uses regular div cards with bg-firefly-darkgray
- Simple hover transform (translate-y-2)
- Basic shadow effects
- Standard grid layout

**Target EXPLORE OUR MENU styling:**
- Uses 3D CardContainer/CardBody components
- Gradient backgrounds (from-firefly-red to-[#8B0000])
- 3D hover effects with perspective and rotation
- CardItem components with translateZ effects
- Enhanced shadows and borders
- Bounce-in animations with staggered delays

### Progress
- [x] Import 3D card components into FeaturedBurgers
- [x] Replace regular div cards with CardContainer/CardBody
- [x] Apply gradient backgrounds and 3D effects
- [x] Add CardItem wrappers for enhanced depth
- [x] Implement intersection observer for animations
- [x] Add bounce-in animations with staggered delays
- [x] Add bounce-in keyframes animation to CSS
- [x] Update background color to match menu section (firefly-cream)
- [x] Update text colors for better contrast
- [x] Add loading state with animated burger icon
- [x] Make images take full width of the card
- [x] Test and refine the visual effects
- [x] Create new branch 'feature/featured-burgers-3d-styling'
- [x] Commit changes with descriptive message
- [x] Push branch to remote repository
- [x] Update background to match hero section (firefly-red gradient)
- [x] Change card backgrounds to white
- [x] Update text colors for proper contrast on white cards
- [x] Update loading spinner color to white
- [x] Change section background to white
- [x] Update text colors for white background contrast
- [x] Update loading spinner color for white background
- [x] Reduce card shadow from shadow-lg to shadow-sm
- [x] Commit updated styling changes
- [x] Push changes to remote repository
- [x] Replace Add to Cart button with plus icon in bottom right corner
- [x] Import Plus icon from lucide-react
- [x] Reposition View Details button to full width
- [x] Add proper spacing and hover effects for plus button
- [x] Update plus button to take half of bottom right corner
- [x] Change to quarter-circle shape (rounded-tl-full)
- [x] Increase button size to w-16 h-16
- [x] Position flush with bottom right corner
- [x] Center icon with translate adjustments
- [x] Increase plus icon size from 24 to 32
- [x] Increase plus icon size further to 40
- [x] Update card style to match category cards:
  - [x] Apply red gradient background (from-firefly-red to-[#8B0000])
  - [x] Center content layout with proper spacing
  - [x] Use category card dimensions (aspect-[1/1.2] h-[450px])
  - [x] Apply proper 3D effects and shadows
  - [x] Update text colors for red background (white/cream)
  - [x] Center image with proper sizing (w-44 h-44)
  - [x] Update plus button to white/transparent style
  - [x] Make images take full width of card
  - [x] Remove padding from CardBody and add overflow-hidden
  - [x] Use negative margins to extend image to card edges
  - [x] Move padding to content area for proper spacing
- [x] Commit category card styling changes
- [x] Push changes to remote repository
- [x] Update section background to match hero section (red gradient)
- [x] Update text colors for red background contrast
- [x] Update loading spinner and text colors for visibility
- [x] Remove border from cards for cleaner appearance
- [x] Remove border from plus button for seamless glass effect
- [x] Commit background color and border removal changes
- [x] Push changes to remote repository

### Current Analysis
The BurgerCategories component includes:
- 3D card effects using CardContainer/CardBody from ui/3d-card
- Expandable category details with smooth transitions
- Loading state with animated burger icon
- Intersection Observer for scroll-triggered animations
- Staggered card animations with bounce-in effects
- Six categories: Beef Burgers, Chicken Burgers, Veggie Burgers, Loaded Fries, Drinks & Shakes, Sides
- Detailed category information with featured items
- Responsive grid layout (1/2/3 columns)

### Previous Completed Task: Hero Section Update
- Updated the hero section's background color to match branding
- Removed background image
- Added comments to explain changes
- Added burger image next to hero text
- Centered menu items horizontally
- Positioned logo above menu items
- Increased size of hero image
- Increased font size of menu items
- Enhanced hover effects for menu items
- Increased top margin between logo and menu
- Redesigned hero section with "IS BACK" style stacked text and integrated burger image
- Duplicated "ABOVE ALL BURGERS" phrase three times vertically
- Ensured each phrase appears on a single line without wrapping
- Removed margins between text lines for tighter stacking
- Added margin between menu items and the first line of text
- Further increased the size of "ABOVE ALL BURGERS" text and burger image
- Further increased the bottom margin between menu items and the first line of text
- Further increased the size of the burger image
- Added white border hover effect to menu items
- Changed menu hover text color to white
- Changed Order Online button to dark red
- Removed Order Now button from hero section

### Progress
- [x] Created new branch 'hero-section-update'
- [x] Updated background color from firefly-black to firefly-red
- [x] Removed background image
- [x] Added comments explaining all changes
- [x] Created unit test for HeroSection component
- [x] Commit changes
- [x] Push branch to remote repository
- [x] Added burger image next to hero text
- [x] Created float animation for the burger image
- [x] Updated unit test to verify burger image
- [x] Commit latest changes
- [x] Push changes to remote repository
- [x] Centered menu items horizontally
- [x] Positioned logo above menu items
- [x] Increased size of hero image
- [x] Commit layout changes
- [x] Push changes to remote repository
- [x] Increased font size of menu items
- [x] Enhanced hover effects for menu items
- [x] Increased top margin between logo and menu
- [x] Commit menu enhancements
- [x] Push changes to remote repository
- [x] Redesigned hero section with "IS BACK" style stacked text
- [x] Integrated burger image into the text design
- [x] Added cream color to the Tailwind configuration
- [x] Created new branch 'hero-section-redesign'
- [x] Committed hero section redesign
- [x] Pushed branch to remote repository
- [x] Duplicated "ABOVE ALL BURGERS" phrase three times vertically
- [x] Ensured each phrase appears on a single line without wrapping
- [x] Adjusted burger image size for better proportion
- [x] Commit updated hero section design
- [x] Push changes to remote repository
- [x] Removed margins between text lines for tighter stacking
- [x] Commit margin adjustments
- [x] Push changes to remote repository
- [x] Added margin between menu items and the first line of text
- [x] Commit spacing adjustments
- [x] Push changes to remote repository
- [x] Further increased the size of "ABOVE ALL BURGERS" text and burger image
- [x] Commit size increase changes
- [x] Push changes to remote repository
- [x] Further increased the bottom margin between menu items and the first line of text
- [x] Commit spacing adjustments
- [x] Push changes to remote repository
- [x] Further increased the size of the burger image
- [x] Commit image size increase changes
- [x] Push changes to remote repository
- [x] Added white border hover effect to menu items
- [x] Commit menu hover effect changes
- [x] Push changes to remote repository
- [x] Changed menu hover text color to white
- [x] Changed Order Online button to dark red
- [x] Removed Order Now button from hero section
- [x] Commit final UI adjustments
- [x] Push changes to remote repository

### Summary of Changes
- Changed the hero section background from firefly-black to firefly-red to match branding
- Removed the background image to use a solid color instead
- Added detailed comments explaining all changes
- Created a unit test to verify the changes
- Installed testing dependencies (@testing-library/react, @testing-library/jest-dom, jest, @types/jest)
- Added burger image next to hero text in a responsive two-column layout
- Created a floating animation effect for the burger image
- Added a subtle glow effect behind the burger image
- Updated unit test to verify the burger image is present
- Restructured the navigation menu to be centered horizontally
- Positioned the logo above the menu items for better visual hierarchy
- Increased the size of the hero burger image for greater visual impact
  - Mobile: 80px × 80px → 320px × 320px
  - Tablet: 96px × 96px → 500px × 500px
  - Desktop: 96px × 96px → 600px × 600px
- Enhanced menu items for better user experience:
  - Increased font size from base to text-lg (desktop) and text-2xl (mobile)
  - Added sophisticated hover effects with animated underlines
  - Improved spacing between menu items and increased margin between logo and menu
  - Enhanced button styling with shadows and smoother hover transitions
  - Added subtle background effect to the cart icon on hover
  - Added white border hover effect to menu items for better visual feedback
  - Applied consistent hover styling to both desktop and mobile navigation items
  - Changed menu hover text color from red to white for better contrast
  - Updated Order Online button to dark red (bg-red-900) for more visual distinction
- Completely redesigned the hero section with "IS BACK" style inspiration:
  - Created a bold, stacked text layout with "ABOVE ALL BURGERS" in large typography
  - Integrated the burger image directly into the text design
  - Added a cream color (#FFF8E1) to the Tailwind configuration for the text
  - Centered all content for maximum impact
  - Maintained responsive design across all screen sizes
  - Further enhanced the design by duplicating "ABOVE ALL BURGERS" three times vertically
  - Used whitespace-nowrap to ensure each phrase appears on a single line without wrapping
  - Removed all margins between text lines for a tighter, more impactful stacking effect
  - Applied custom leading and tracking for perfect text alignment
  - Positioned the burger image absolutely in the center for a dramatic overlay effect
  - Added appropriate spacing between the menu items and the hero text
  - Implemented responsive padding that increases with screen size
  - Further increased text size for maximum impact:
    - Increased from 4xl-8xl to 5xl-9xl across all breakpoints
  - Further increased the bottom margin between menu items and the first line of text:
    - Mobile: Increased from 16px to 24px padding
    - Tablet: Increased from 24px to 32px padding
    - Desktop: Increased from 32px to 40px padding
  - Dramatically increased the burger image size for maximum visual impact:
    - Mobile: Increased to 72px × 72px (from 64px × 64px)
    - Small screens: Increased to 96px × 96px (from 80px × 80px)
    - Medium screens: Increased to 128px × 128px (from 96px × 96px)
    - Large screens: Increased to 144px × 144px (from 128px × 128px)
    - Expanded the glow effect from -inset-6 to -inset-8 for better visibility
  - Simplified the call-to-action area by removing the Order Now button
    - Focused user attention on the single View Menu button
    - Created a cleaner, more focused design

### Project Structure
- **Tech Stack**: 
  - Vite
  - React
  - TypeScript
  - Tailwind CSS
  - shadcn-ui components
  - React Router for navigation
  - React Query for data fetching

### Key Components
- [x] Navbar: Responsive navigation with mobile menu
- [x] HeroSection: Main landing section with call-to-action buttons
- [x] BurgerCategories: Grid display of different menu categories
- [x] FeaturedBurgers: Showcases popular burger items with details
- [x] InstagramFeed: Carousel of Instagram posts using embla-carousel
- [x] Footer: Contact information, social links, and newsletter signup

### Features
- Responsive design for mobile and desktop
- Modern UI with consistent branding (firefly-red as primary color)
- Instagram integration (currently using mock data)
- Interactive elements (hover effects, animations)

### Next Steps
- Create a new branch for any future development
- Consider potential improvements:
  - Menu page with full product listings
  - Online ordering functionality
  - Location/map integration
  - Authentication for user accounts
  - Admin dashboard for content management
