// Mock Data for FireFly Burger Application

import { MenuItem, Branch, SubscriptionPlan } from './localStorage';

export const mockMenuItems: MenuItem[] = [
  {
    id: 'burger_001',
    name: 'FireFly Classic',
    description: 'Our signature beef burger with lettuce, tomato, onion, and our special FireFly sauce',
    price: 12.99,
    image: 'https://images.unsplash.com/photo-1586190848861-99aa4a171e90?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1280&q=80',
    category: 'Beef Burgers',
    ingredients: ['Beef Patty', 'Lettuce', 'Tomato', 'Onion', 'FireFly Sauce', 'Brioche Bun'],
    allergens: ['Gluten', 'Eggs'],
    nutritionalInfo: {
      calories: 650,
      protein: 35,
      carbs: 45,
      fat: 32,
    },
    customizations: {
      sizes: [
        { name: 'Regular', price: 0 },
        { name: 'Large', price: 2.50 },
      ],
      addOns: [
        { name: 'Extra Cheese', price: 1.50 },
        { name: 'Bacon', price: 2.00 },
        { name: 'Avocado', price: 1.75 },
        { name: 'Extra Patty', price: 4.00 },
      ],
    },
    isPopular: true,
    isSpicy: false,
  },
  {
    id: 'burger_002',
    name: 'Spicy Inferno',
    description: 'For the brave souls! Spicy beef patty with jalapeños, pepper jack cheese, and ghost pepper sauce',
    price: 14.99,
    image: 'https://images.unsplash.com/photo-1551782450-17144efb9c50?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1169&q=80',
    category: 'Beef Burgers',
    ingredients: ['Spicy Beef Patty', 'Jalapeños', 'Pepper Jack Cheese', 'Ghost Pepper Sauce', 'Brioche Bun'],
    allergens: ['Gluten', 'Dairy'],
    nutritionalInfo: {
      calories: 720,
      protein: 38,
      carbs: 42,
      fat: 38,
    },
    customizations: {
      sizes: [
        { name: 'Regular', price: 0 },
        { name: 'Large', price: 2.50 },
      ],
      addOns: [
        { name: 'Extra Cheese', price: 1.50 },
        { name: 'Extra Jalapeños', price: 1.00 },
        { name: 'Cooling Ranch', price: 0.75 },
      ],
    },
    isPopular: true,
    isSpicy: true,
  },
  {
    id: 'burger_003',
    name: 'Crispy Chicken Deluxe',
    description: 'Crispy fried chicken breast with coleslaw, pickles, and honey mustard sauce',
    price: 13.49,
    image: 'https://images.unsplash.com/photo-1606755962773-d324e0a13086?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=687&q=80',
    category: 'Chicken Burgers',
    ingredients: ['Crispy Chicken Breast', 'Coleslaw', 'Pickles', 'Honey Mustard', 'Brioche Bun'],
    allergens: ['Gluten', 'Eggs'],
    nutritionalInfo: {
      calories: 680,
      protein: 42,
      carbs: 48,
      fat: 28,
    },
    customizations: {
      sizes: [
        { name: 'Regular', price: 0 },
        { name: 'Large', price: 2.50 },
      ],
      addOns: [
        { name: 'Extra Chicken', price: 3.50 },
        { name: 'Cheese', price: 1.50 },
        { name: 'Bacon', price: 2.00 },
      ],
    },
    isPopular: false,
    isSpicy: false,
  },
  {
    id: 'burger_004',
    name: 'Garden Veggie Supreme',
    description: 'Plant-based patty with fresh vegetables, hummus, and tahini dressing',
    price: 11.99,
    image: 'https://images.unsplash.com/photo-1550317138-10000687a72b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1220&q=80',
    category: 'Veggie Burgers',
    ingredients: ['Plant-Based Patty', 'Lettuce', 'Tomato', 'Cucumber', 'Hummus', 'Tahini', 'Whole Wheat Bun'],
    allergens: ['Gluten', 'Sesame'],
    nutritionalInfo: {
      calories: 520,
      protein: 22,
      carbs: 58,
      fat: 18,
    },
    customizations: {
      sizes: [
        { name: 'Regular', price: 0 },
        { name: 'Large', price: 2.50 },
      ],
      addOns: [
        { name: 'Avocado', price: 1.75 },
        { name: 'Vegan Cheese', price: 2.00 },
        { name: 'Extra Hummus', price: 1.00 },
      ],
    },
    isPopular: false,
    isSpicy: false,
  },
  {
    id: 'fries_001',
    name: 'Loaded Cheese Fries',
    description: 'Crispy fries topped with melted cheese, bacon bits, and green onions',
    price: 8.99,
    image: 'https://images.unsplash.com/photo-1573080496219-bb080dd4f877?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1074&q=80',
    category: 'Loaded Fries',
    ingredients: ['French Fries', 'Cheddar Cheese', 'Bacon Bits', 'Green Onions', 'Sour Cream'],
    allergens: ['Dairy'],
    nutritionalInfo: {
      calories: 480,
      protein: 12,
      carbs: 52,
      fat: 24,
    },
    customizations: {
      sizes: [
        { name: 'Regular', price: 0 },
        { name: 'Large', price: 3.00 },
      ],
      addOns: [
        { name: 'Extra Cheese', price: 1.50 },
        { name: 'Extra Bacon', price: 2.00 },
        { name: 'Jalapeños', price: 0.75 },
      ],
    },
    isPopular: true,
    isSpicy: false,
  },
  {
    id: 'drink_001',
    name: 'FireFly Milkshake',
    description: 'Creamy vanilla milkshake with a hint of cinnamon and whipped cream',
    price: 5.99,
    image: 'https://images.unsplash.com/photo-1572490122747-3968b75cc699?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80',
    category: 'Drinks & Shakes',
    ingredients: ['Vanilla Ice Cream', 'Milk', 'Cinnamon', 'Whipped Cream', 'Cherry'],
    allergens: ['Dairy'],
    nutritionalInfo: {
      calories: 420,
      protein: 8,
      carbs: 58,
      fat: 16,
    },
    customizations: {
      sizes: [
        { name: 'Regular', price: 0 },
        { name: 'Large', price: 2.00 },
      ],
      addOns: [
        { name: 'Extra Whipped Cream', price: 0.50 },
        { name: 'Chocolate Syrup', price: 0.75 },
        { name: 'Caramel Syrup', price: 0.75 },
      ],
    },
    isPopular: false,
    isSpicy: false,
  },
];

export const mockBranches: Branch[] = [
  {
    id: 'branch_us_001',
    name: 'FireFly Burger Downtown',
    address: '123 Main Street',
    city: 'New York',
    country: 'United States',
    phone: '+****************',
    email: '<EMAIL>',
    hours: {
      monday: { open: '10:00', close: '22:00', isOpen: true },
      tuesday: { open: '10:00', close: '22:00', isOpen: true },
      wednesday: { open: '10:00', close: '22:00', isOpen: true },
      thursday: { open: '10:00', close: '22:00', isOpen: true },
      friday: { open: '10:00', close: '23:00', isOpen: true },
      saturday: { open: '10:00', close: '23:00', isOpen: true },
      sunday: { open: '11:00', close: '21:00', isOpen: true },
    },
    services: ['Dine-in', 'Takeaway', 'Delivery'],
    coordinates: { lat: 40.7128, lng: -74.0060 },
    currency: 'USD',
    currencySymbol: '$',
  },
  {
    id: 'branch_us_002',
    name: 'FireFly Burger Mall Plaza',
    address: '456 Shopping Center Blvd',
    city: 'Los Angeles',
    country: 'United States',
    phone: '+****************',
    email: '<EMAIL>',
    hours: {
      monday: { open: '11:00', close: '21:00', isOpen: true },
      tuesday: { open: '11:00', close: '21:00', isOpen: true },
      wednesday: { open: '11:00', close: '21:00', isOpen: true },
      thursday: { open: '11:00', close: '21:00', isOpen: true },
      friday: { open: '11:00', close: '22:00', isOpen: true },
      saturday: { open: '10:00', close: '22:00', isOpen: true },
      sunday: { open: '11:00', close: '21:00', isOpen: true },
    },
    services: ['Dine-in', 'Takeaway'],
    coordinates: { lat: 34.0522, lng: -118.2437 },
    currency: 'USD',
    currencySymbol: '$',
  },
  {
    id: 'branch_uk_001',
    name: 'FireFly Burger London',
    address: '789 Oxford Street',
    city: 'London',
    country: 'United Kingdom',
    phone: '+44 20 7123 4567',
    email: '<EMAIL>',
    hours: {
      monday: { open: '10:00', close: '22:00', isOpen: true },
      tuesday: { open: '10:00', close: '22:00', isOpen: true },
      wednesday: { open: '10:00', close: '22:00', isOpen: true },
      thursday: { open: '10:00', close: '22:00', isOpen: true },
      friday: { open: '10:00', close: '23:00', isOpen: true },
      saturday: { open: '10:00', close: '23:00', isOpen: true },
      sunday: { open: '11:00', close: '21:00', isOpen: true },
    },
    services: ['Dine-in', 'Takeaway', 'Delivery'],
    coordinates: { lat: 51.5074, lng: -0.1278 },
    currency: 'GBP',
    currencySymbol: '£',
  },
  {
    id: 'branch_ca_001',
    name: 'FireFly Burger Toronto',
    address: '321 Queen Street West',
    city: 'Toronto',
    country: 'Canada',
    phone: '+****************',
    email: '<EMAIL>',
    hours: {
      monday: { open: '10:00', close: '22:00', isOpen: true },
      tuesday: { open: '10:00', close: '22:00', isOpen: true },
      wednesday: { open: '10:00', close: '22:00', isOpen: true },
      thursday: { open: '10:00', close: '22:00', isOpen: true },
      friday: { open: '10:00', close: '23:00', isOpen: true },
      saturday: { open: '10:00', close: '23:00', isOpen: true },
      sunday: { open: '11:00', close: '21:00', isOpen: true },
    },
    services: ['Dine-in', 'Takeaway', 'Delivery'],
    coordinates: { lat: 43.6532, lng: -79.3832 },
    currency: 'CAD',
    currencySymbol: 'C$',
  },
];

export const subscriptionPlans: SubscriptionPlan[] = [
  {
    id: 'juicy',
    name: 'juicy',
    displayName: 'Juicy Plan',
    price: 9.99,
    benefits: [
      'Exclusive menu items and early access to new products',
      '2x reward points on all orders',
      'Free delivery on orders over $15',
      'Monthly surprise treats and discounts',
      'Priority customer support',
    ],
    pointsMultiplier: 2,
    freeDeliveryThreshold: 15,
  },
  {
    id: 'fluffy',
    name: 'fluffy',
    displayName: 'Fluffy Plan',
    price: 19.99,
    benefits: [
      'All Juicy plan benefits included',
      '3x reward points on all orders',
      'Free delivery on all orders (no minimum)',
      'Weekly exclusive offers and flash sales',
      'VIP access to special events and tastings',
      'Personalized meal recommendations',
    ],
    pointsMultiplier: 3,
    freeDeliveryThreshold: 0,
  },
];

// Initialize default data if not exists
export const initializeDefaultData = () => {
  // Set default branch if none selected
  const currentBranch = localStorage.getItem('firefly_current_branch');
  if (!currentBranch) {
    localStorage.setItem('firefly_current_branch', JSON.stringify(mockBranches[0]));
  }
  
  // Initialize rewards account if not exists
  const rewards = localStorage.getItem('firefly_rewards');
  if (!rewards) {
    localStorage.setItem('firefly_rewards', JSON.stringify({
      points: 150,
      tier: 'bronze',
      totalEarned: 150,
      totalRedeemed: 0,
      history: [
        {
          id: 'welcome_bonus',
          type: 'earned',
          points: 150,
          description: 'Welcome bonus for joining FireFly Burger!',
          date: new Date().toISOString(),
        },
      ],
    }));
  }
};

// Helper function to get menu item by ID
export const getMenuItemById = (id: string): MenuItem | undefined => {
  return mockMenuItems.find(item => item.id === id);
};

// Helper function to get branch by ID
export const getBranchById = (id: string): Branch | undefined => {
  return mockBranches.find(branch => branch.id === id);
};

// Helper function to get menu items by category
export const getMenuItemsByCategory = (category: string): MenuItem[] => {
  return mockMenuItems.filter(item => item.category === category);
};

// Helper function to get popular items
export const getPopularItems = (): MenuItem[] => {
  return mockMenuItems.filter(item => item.isPopular);
};

// Helper function to search menu items
export const searchMenuItems = (query: string): MenuItem[] => {
  const lowercaseQuery = query.toLowerCase();
  return mockMenuItems.filter(item => 
    item.name.toLowerCase().includes(lowercaseQuery) ||
    item.description.toLowerCase().includes(lowercaseQuery) ||
    item.category.toLowerCase().includes(lowercaseQuery) ||
    item.ingredients.some(ingredient => ingredient.toLowerCase().includes(lowercaseQuery))
  );
};
