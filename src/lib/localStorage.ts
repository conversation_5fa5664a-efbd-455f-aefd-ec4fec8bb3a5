// Local Storage Utility Functions for FireFly Burger

export interface MenuItem {
  id: string;
  name: string;
  description: string;
  price: number;
  image: string;
  category: string;
  ingredients: string[];
  allergens: string[];
  nutritionalInfo: {
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
  };
  customizations: {
    sizes: { name: string; price: number }[];
    addOns: { name: string; price: number }[];
  };
  isPopular: boolean;
  isSpicy: boolean;
}

export interface CartItem {
  id: string;
  menuItemId: string;
  name: string;
  price: number;
  image: string;
  quantity: number;
  size?: string;
  addOns: string[];
  totalPrice: number;
}

export interface Address {
  id: string;
  name: string;
  street: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
  isDefault: boolean;
  deliveryInstructions?: string;
}

export interface Branch {
  id: string;
  name: string;
  address: string;
  city: string;
  country: string;
  phone: string;
  email: string;
  hours: {
    [key: string]: { open: string; close: string; isOpen: boolean };
  };
  services: string[];
  coordinates: { lat: number; lng: number };
  currency: string;
  currencySymbol: string;
}

export interface Order {
  id: string;
  items: CartItem[];
  subtotal: number;
  tax: number;
  deliveryFee: number;
  total: number;
  status: 'pending' | 'confirmed' | 'preparing' | 'ready' | 'delivered' | 'cancelled';
  deliveryAddress: Address;
  orderDate: string;
  estimatedDelivery: string;
  paymentMethod: string;
}

export interface SubscriptionPlan {
  id: string;
  name: 'juicy' | 'fluffy';
  displayName: string;
  price: number;
  benefits: string[];
  pointsMultiplier: number;
  freeDeliveryThreshold: number;
}

export interface UserSubscription {
  planId: string;
  startDate: string;
  nextBillingDate: string;
  status: 'active' | 'cancelled' | 'paused';
}

export interface RewardsAccount {
  points: number;
  tier: 'bronze' | 'silver' | 'gold' | 'platinum';
  totalEarned: number;
  totalRedeemed: number;
  history: {
    id: string;
    type: 'earned' | 'redeemed';
    points: number;
    description: string;
    date: string;
  }[];
}

// Local Storage Keys
const STORAGE_KEYS = {
  CART: 'firefly_cart',
  ADDRESSES: 'firefly_addresses',
  CURRENT_BRANCH: 'firefly_current_branch',
  ORDERS: 'firefly_orders',
  SUBSCRIPTION: 'firefly_subscription',
  REWARDS: 'firefly_rewards',
  USER_PREFERENCES: 'firefly_preferences',
} as const;

// Generic Local Storage Functions
export const getFromStorage = <T>(key: string, defaultValue: T): T => {
  try {
    const item = localStorage.getItem(key);
    return item ? JSON.parse(item) : defaultValue;
  } catch (error) {
    console.error(`Error reading from localStorage key "${key}":`, error);
    return defaultValue;
  }
};

export const setToStorage = <T>(key: string, value: T): void => {
  try {
    localStorage.setItem(key, JSON.stringify(value));
  } catch (error) {
    console.error(`Error writing to localStorage key "${key}":`, error);
  }
};

export const removeFromStorage = (key: string): void => {
  try {
    localStorage.removeItem(key);
  } catch (error) {
    console.error(`Error removing from localStorage key "${key}":`, error);
  }
};

// Cart Management
export const getCart = (): CartItem[] => {
  return getFromStorage(STORAGE_KEYS.CART, []);
};

export const setCart = (cart: CartItem[]): void => {
  setToStorage(STORAGE_KEYS.CART, cart);
};

export const addToCart = (item: CartItem): void => {
  const cart = getCart();
  const existingItemIndex = cart.findIndex(
    cartItem => 
      cartItem.menuItemId === item.menuItemId && 
      cartItem.size === item.size &&
      JSON.stringify(cartItem.addOns.sort()) === JSON.stringify(item.addOns.sort())
  );

  if (existingItemIndex > -1) {
    cart[existingItemIndex].quantity += item.quantity;
    cart[existingItemIndex].totalPrice = cart[existingItemIndex].price * cart[existingItemIndex].quantity;
  } else {
    cart.push(item);
  }
  
  setCart(cart);
};

export const removeFromCart = (itemId: string): void => {
  const cart = getCart();
  const updatedCart = cart.filter(item => item.id !== itemId);
  setCart(updatedCart);
};

export const updateCartItemQuantity = (itemId: string, quantity: number): void => {
  const cart = getCart();
  const itemIndex = cart.findIndex(item => item.id === itemId);
  
  if (itemIndex > -1) {
    if (quantity <= 0) {
      removeFromCart(itemId);
    } else {
      cart[itemIndex].quantity = quantity;
      cart[itemIndex].totalPrice = cart[itemIndex].price * quantity;
      setCart(cart);
    }
  }
};

export const clearCart = (): void => {
  setCart([]);
};

export const getCartTotal = (): number => {
  const cart = getCart();
  return cart.reduce((total, item) => total + item.totalPrice, 0);
};

export const getCartItemCount = (): number => {
  const cart = getCart();
  return cart.reduce((count, item) => count + item.quantity, 0);
};

// Address Management
export const getAddresses = (): Address[] => {
  return getFromStorage(STORAGE_KEYS.ADDRESSES, []);
};

export const setAddresses = (addresses: Address[]): void => {
  setToStorage(STORAGE_KEYS.ADDRESSES, addresses);
};

export const addAddress = (address: Omit<Address, 'id'>): Address => {
  const addresses = getAddresses();
  const newAddress: Address = {
    ...address,
    id: `addr_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
  };
  
  // If this is set as default, remove default from others
  if (newAddress.isDefault) {
    addresses.forEach(addr => addr.isDefault = false);
  }
  
  addresses.push(newAddress);
  setAddresses(addresses);
  return newAddress;
};

export const updateAddress = (id: string, updates: Partial<Address>): void => {
  const addresses = getAddresses();
  const addressIndex = addresses.findIndex(addr => addr.id === id);
  
  if (addressIndex > -1) {
    // If setting as default, remove default from others
    if (updates.isDefault) {
      addresses.forEach(addr => addr.isDefault = false);
    }
    
    addresses[addressIndex] = { ...addresses[addressIndex], ...updates };
    setAddresses(addresses);
  }
};

export const deleteAddress = (id: string): void => {
  const addresses = getAddresses();
  const updatedAddresses = addresses.filter(addr => addr.id !== id);
  setAddresses(updatedAddresses);
};

export const getDefaultAddress = (): Address | null => {
  const addresses = getAddresses();
  return addresses.find(addr => addr.isDefault) || null;
};

// Branch Management
export const getCurrentBranch = (): Branch | null => {
  return getFromStorage(STORAGE_KEYS.CURRENT_BRANCH, null);
};

export const setCurrentBranch = (branch: Branch): void => {
  setToStorage(STORAGE_KEYS.CURRENT_BRANCH, branch);
};

// Order Management
export const getOrders = (): Order[] => {
  return getFromStorage(STORAGE_KEYS.ORDERS, []);
};

export const addOrder = (order: Omit<Order, 'id'>): Order => {
  const orders = getOrders();
  const newOrder: Order = {
    ...order,
    id: `order_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
  };
  
  orders.unshift(newOrder); // Add to beginning for recent orders first
  setToStorage(STORAGE_KEYS.ORDERS, orders);
  return newOrder;
};

// Subscription Management
export const getSubscription = (): UserSubscription | null => {
  return getFromStorage(STORAGE_KEYS.SUBSCRIPTION, null);
};

export const setSubscription = (subscription: UserSubscription): void => {
  setToStorage(STORAGE_KEYS.SUBSCRIPTION, subscription);
};

export const cancelSubscription = (): void => {
  removeFromStorage(STORAGE_KEYS.SUBSCRIPTION);
};

// Rewards Management
export const getRewards = (): RewardsAccount => {
  return getFromStorage(STORAGE_KEYS.REWARDS, {
    points: 0,
    tier: 'bronze',
    totalEarned: 0,
    totalRedeemed: 0,
    history: [],
  });
};

export const setRewards = (rewards: RewardsAccount): void => {
  setToStorage(STORAGE_KEYS.REWARDS, rewards);
};

export const addRewardPoints = (points: number, description: string): void => {
  const rewards = getRewards();
  rewards.points += points;
  rewards.totalEarned += points;
  rewards.history.unshift({
    id: `reward_${Date.now()}`,
    type: 'earned',
    points,
    description,
    date: new Date().toISOString(),
  });
  
  // Update tier based on total earned
  if (rewards.totalEarned >= 10000) rewards.tier = 'platinum';
  else if (rewards.totalEarned >= 5000) rewards.tier = 'gold';
  else if (rewards.totalEarned >= 2000) rewards.tier = 'silver';
  else rewards.tier = 'bronze';
  
  setRewards(rewards);
};

export const redeemRewardPoints = (points: number, description: string): boolean => {
  const rewards = getRewards();
  
  if (rewards.points < points) {
    return false; // Insufficient points
  }
  
  rewards.points -= points;
  rewards.totalRedeemed += points;
  rewards.history.unshift({
    id: `redeem_${Date.now()}`,
    type: 'redeemed',
    points,
    description,
    date: new Date().toISOString(),
  });
  
  setRewards(rewards);
  return true;
};
