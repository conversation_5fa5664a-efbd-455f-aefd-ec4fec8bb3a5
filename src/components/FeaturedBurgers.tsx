import { But<PERSON> } from "@/components/ui/button";
import { useState, useEffect, useRef } from "react";
import { Plus } from "lucide-react";
import { useCart } from "@/contexts/CartContext";
import { getPopularItems } from "@/lib/mockData";
import { CartItem } from "@/lib/localStorage";
import { useNavigate } from "react-router-dom";

interface BurgerItemProps {
  id: string;
  name: string;
  description: string;
  price: number;
  image: string;
  spicy?: boolean;
  popular?: boolean;
  index?: number;
  isVisible?: boolean;
  onAddToCart: (id: string) => void;
  onViewDetails: (id: string) => void;
}

const BurgerItem = ({ id, name, description, price, image, spicy, popular, index, isVisible, onAddToCart, onViewDetails }: BurgerItemProps) => {
  return (
    <div className={`w-full aspect-[1/1.1] h-[400px] transition-all duration-500 ${isVisible ? 'animate-bounce-in opacity-100' : 'opacity-0 translate-y-10'}`} style={{ animationDelay: `${index ? index * 100 : 0}ms` }}>
      <div className="bg-white rounded-xl w-full h-full shadow-md hover:shadow-lg transition-shadow duration-300 relative overflow-hidden border border-gray-100">
        <div className="flex flex-col h-full">
          {/* Image Section - Clickable */}
          <div
            className="relative h-[200px] overflow-hidden cursor-pointer"
            onClick={() => onViewDetails(id)}
          >
            <img
              src={image}
              alt={name}
              className="object-cover w-full h-full hover:scale-105 transition-transform duration-300"
            />

            {spicy && (
              <div className="absolute top-2 right-2">
                <span className="bg-orange-500 text-white text-xs font-bold px-2 py-1 rounded-full shadow-lg">
                  SPICY
                </span>
              </div>
            )}

            {popular && (
              <div className="absolute top-2 left-2">
                <span className="bg-yellow-500 text-black text-xs font-bold px-2 py-1 rounded-full shadow-lg">
                  POPULAR
                </span>
              </div>
            )}
          </div>

          {/* Content Section */}
          <div className="flex flex-col justify-between text-center p-4 flex-1">
            <div className="space-y-2">
              <h3
                className="text-lg font-bold text-gray-900 cursor-pointer hover:text-firefly-red transition-colors duration-200"
                onClick={() => onViewDetails(id)}
              >
                {name}
              </h3>
              <span className="text-firefly-red font-bold text-lg">${price.toFixed(2)}</span>
              <p className="text-gray-600 text-sm leading-relaxed">{description}</p>
            </div>

            {/* Add to Cart Button - Under description */}
            <div className="mt-4">
              <Button
                onClick={(e) => {
                  e.stopPropagation();
                  onAddToCart(id);
                }}
                className="w-full bg-firefly-red hover:bg-red-700 text-white py-2 px-4 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 hover:scale-105 flex items-center justify-center gap-2"
              >
                <Plus size={16} />
                Add to Cart
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

const FeaturedBurgers = () => {
  const [visibleCards, setVisibleCards] = useState<boolean[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const sectionRef = useRef<HTMLElement>(null);
  const { addToCart } = useCart();
  const navigate = useNavigate();

  const burgers = getPopularItems();

  const handleAddToCart = (itemId: string) => {
    const item = burgers.find(burger => burger.id === itemId);
    if (!item) return;

    const cartItem: CartItem = {
      id: `cart_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      menuItemId: item.id,
      name: item.name,
      price: item.price,
      image: item.image,
      quantity: 1,
      addOns: [],
      totalPrice: item.price,
    };

    addToCart(cartItem);
  };

  const handleViewDetails = (itemId: string) => {
    navigate(`/items/${itemId}`);
  };

  useEffect(() => {
    // Simulate loading
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    if (isLoading) return;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const cards = entry.target.querySelectorAll('[data-card-index]');
            const newVisibleCards = [...visibleCards];

            cards.forEach((card, index) => {
              setTimeout(() => {
                setVisibleCards(prev => {
                  const updated = [...prev];
                  updated[index] = true;
                  return updated;
                });
              }, index * 100);
            });
          }
        });
      },
      { threshold: 0.1 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => observer.disconnect();
  }, [isLoading]);

  return (
    <section ref={sectionRef} className="py-20 bg-white relative overflow-hidden">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="hero-title text-3xl md:text-4xl text-firefly-black mb-4">TRENDING <span className="text-firefly-red">ITEMS</span></h2>
          <p className="text-gray-600 max-w-lg mx-auto">
            Our most popular items that customers can't get enough of.
          </p>
        </div>

        {isLoading ? (
          <div className="flex flex-col items-center justify-center py-16">
            <div className="relative">
              <div className="w-16 h-16 border-4 border-firefly-red border-t-transparent rounded-full animate-spin"></div>
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="w-12 h-12 bg-white rounded-full"></div>
              </div>
              <div className="absolute inset-0 flex items-center justify-center">
                <img
                  src="/burger-top.png"
                  alt="Firefly Burger"
                  className="w-10 h-10 object-contain animate-pulse"
                />
              </div>
            </div>
            <p className="mt-4 text-firefly-red font-medium">Loading trending items...</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-32 place-items-center">
            {burgers.map((burger, index) => (
              <div key={burger.id} data-card-index={index}>
                <BurgerItem
                  id={burger.id}
                  name={burger.name}
                  description={burger.description}
                  price={burger.price}
                  image={burger.image}
                  spicy={burger.isSpicy}
                  popular={burger.isPopular}
                  index={index}
                  isVisible={visibleCards[index] || false}
                  onAddToCart={handleAddToCart}
                  onViewDetails={handleViewDetails}
                />
              </div>
            ))}
          </div>
        )}
      </div>
    </section>
  );
};

export default FeaturedBurgers;
