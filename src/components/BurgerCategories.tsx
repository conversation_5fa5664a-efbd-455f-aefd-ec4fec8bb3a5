import { Beef, Utensils, Salad, Sandwich, CupSoda } from "lucide-react";
import { useState, useEffect, useRef } from "react";
import { Loader2 } from "lucide-react";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "./ui/carousel";

type CategoryItemProps = {
  image: string;
  title: string;
  description: string;
  index?: number;
  isVisible?: boolean;
};

const CategoryItem = ({
  image,
  title,
  description,
  index,
  isVisible,
}: CategoryItemProps) => {
  return (
    <div className={`w-[280px] h-[350px] transition-all duration-500 ${isVisible ? 'animate-bounce-in opacity-100' : 'opacity-0 translate-y-10'}`} style={{ animationDelay: `${index ? index * 100 : 0}ms` }}>
      <div className="bg-white p-4 rounded-xl w-full h-full shadow-md hover:shadow-lg transition-shadow duration-300 border border-gray-100">
        <div className="flex flex-col items-center text-center h-full">
          <div className="flex items-center justify-center h-[180px]">
            <div className="w-40 h-40">
              <img
                src={image}
                alt={title}
                className="object-contain w-full h-full"
              />
            </div>
          </div>
          <div className="h-[110px] flex flex-col justify-center">
            <div className="w-full">
              <h3 className="text-xl font-bold mb-1 text-gray-900">{title}</h3>
            </div>
            <div className="w-full">
              <p className="text-gray-600 text-sm">{description}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

const BurgerCategories = () => {
  const [visibleCards, setVisibleCards] = useState<boolean[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const sectionRef = useRef<HTMLElement>(null);
  
  useEffect(() => {
    // Simulate loading time (remove in production and replace with actual data fetching)
    const loadingTimer = setTimeout(() => {
      setIsLoading(false);
    }, 1500);

    const currentRef = sectionRef.current;
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && !isLoading) {
          // Start showing cards with staggered delay
          const timer = setTimeout(() => {
            setVisibleCards(Array(6).fill(true)); // Fixed number of categories
          }, 100);
          return () => clearTimeout(timer);
        }
      },
      { threshold: 0.2 } // Trigger when 20% of the section is visible
    );

    if (currentRef) {
      observer.observe(currentRef);
    }

    return () => {
      if (currentRef) {
        observer.unobserve(currentRef);
      }
      clearTimeout(loadingTimer);
    };
  }, [isLoading]);
  
  const categories = [
    {
      image: "/categories/beef-burger.png",
      title: "Beef Burgers",
      description: "Premium beef patties with our signature seasonings"
    },
    {
      image: "/categories/chicken-burger.png",
      title: "Chicken Burgers",
      description: "Juicy chicken with crispy coating and special sauces"
    },
    {
      image: "/categories/veggie-burgers.png",
      title: "Veggie Burgers",
      description: "Plant-based options that don't compromise on flavor"
    },
    {
      image: "/categories/loaded-fries.png",
      title: "Loaded Fries",
      description: "Crispy fries loaded with premium toppings"
    },
    {
      image: "/categories/drinks.png",
      title: "Drinks & Shakes",
      description: "Refreshing beverages and indulgent shakes"
    },
    {
      image: "/categories/sides.png",
      title: "Sides",
      description: "Perfect companions to complete your meal"
    },
  ];

  return (
    <section ref={sectionRef} id="menu" className="py-20 bg-white relative overflow-hidden">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="hero-title text-3xl md:text-4xl text-firefly-black mb-4">EXPLORE OUR <span className="text-firefly-red">MENU</span></h2>
          <p className="text-gray-600 max-w-lg mx-auto">
            Discover our range of handcrafted burgers and sides, each made with premium ingredients and bold flavors.
          </p>
        </div>
        {isLoading ? (
          <div className="flex flex-col items-center justify-center py-16">
            <div className="relative">
              <Loader2 size={64} className="text-firefly-red animate-spin" />
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="w-12 h-12 bg-white rounded-full"></div>
              </div>
              <div className="absolute inset-0 flex items-center justify-center">
                <img 
                  src="/burger-top.png" 
                  alt="Firefly Burger" 
                  className="w-10 h-10 object-contain animate-pulse" 
                />
              </div>
            </div>
            <p className="mt-4 text-firefly-red font-medium">Loading menu categories...</p>
          </div>
        ) : (
          <>
            <div className="relative mb-8">
              <Carousel
                opts={{
                  align: "start",
                  loop: false,
                }}
                className="w-full"
              >
                <CarouselContent className="-ml-2 md:-ml-4 p-2">
                  {categories.map((category, index) => (
                    <CarouselItem key={index} className="pl-2 md:pl-4 basis-auto">
                      <CategoryItem
                        image={category.image}
                        title={category.title}
                        description={category.description}
                        index={index}
                        isVisible={visibleCards[index] || false}
                      />
                    </CarouselItem>
                  ))}
                </CarouselContent>
                <CarouselPrevious className="left-4" />
                <CarouselNext className="right-4" />
              </Carousel>
            </div>
          </>
        )}
      </div>
    </section>
  );
};

export default BurgerCategories;
