import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { ArrowLeft, Plus, Minus, Star, Clock, Users } from 'lucide-react';
import { getMenuItemById } from '@/lib/mockData';
import { useCart } from '@/contexts/CartContext';
import { CartItem, MenuItem } from '@/lib/localStorage';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';

const ItemDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { addToCart } = useCart();
  
  const [item, setItem] = useState<MenuItem | null>(null);
  const [selectedSize, setSelectedSize] = useState<string>('');
  const [selectedAddOns, setSelectedAddOns] = useState<string[]>([]);
  const [quantity, setQuantity] = useState(1);
  const [totalPrice, setTotalPrice] = useState(0);

  useEffect(() => {
    if (id) {
      const menuItem = getMenuItemById(id);
      if (menuItem) {
        setItem(menuItem);
        // Set default size
        if (menuItem.customizations.sizes.length > 0) {
          setSelectedSize(menuItem.customizations.sizes[0].name);
        }
      }
    }
  }, [id]);

  useEffect(() => {
    if (!item) return;

    let price = item.price;
    
    // Add size price
    const sizeOption = item.customizations.sizes.find(size => size.name === selectedSize);
    if (sizeOption) {
      price += sizeOption.price;
    }
    
    // Add add-ons price
    selectedAddOns.forEach(addOnName => {
      const addOn = item.customizations.addOns.find(addon => addon.name === addOnName);
      if (addOn) {
        price += addOn.price;
      }
    });
    
    setTotalPrice(price * quantity);
  }, [item, selectedSize, selectedAddOns, quantity]);

  const handleAddOnChange = (addOnName: string, checked: boolean) => {
    if (checked) {
      setSelectedAddOns(prev => [...prev, addOnName]);
    } else {
      setSelectedAddOns(prev => prev.filter(name => name !== addOnName));
    }
  };

  const handleAddToCart = () => {
    if (!item) return;

    const cartItem: CartItem = {
      id: `cart_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      menuItemId: item.id,
      name: item.name,
      price: totalPrice / quantity,
      image: item.image,
      quantity,
      size: selectedSize,
      addOns: selectedAddOns,
      totalPrice,
    };

    addToCart(cartItem);
    navigate('/');
  };

  if (!item) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Item not found</h2>
          <Button onClick={() => navigate('/')} variant="outline">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Menu
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      <main className="pt-32 pb-16">
        <div className="container mx-auto px-4">
          {/* Back Button */}
          <Button
            onClick={() => navigate('/')}
            variant="ghost"
            className="mb-6"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Menu
          </Button>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Product Image */}
            <div className="space-y-4">
              <div className="relative aspect-square rounded-lg overflow-hidden bg-white shadow-lg">
                <img
                  src={item.image}
                  alt={item.name}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    e.currentTarget.src = '/burger.png';
                  }}
                />
                
                {/* Badges */}
                <div className="absolute top-4 left-4 flex gap-2">
                  {item.isPopular && (
                    <Badge className="bg-yellow-500 text-black">
                      <Star className="w-3 h-3 mr-1" />
                      Popular
                    </Badge>
                  )}
                  {item.isSpicy && (
                    <Badge className="bg-orange-500 text-white">
                      🌶️ Spicy
                    </Badge>
                  )}
                </div>
              </div>
            </div>

            {/* Product Details */}
            <div className="space-y-6">
              <div>
                <h1 className="text-3xl font-bold text-gray-900 mb-2">{item.name}</h1>
                <p className="text-gray-600 text-lg">{item.description}</p>
                <div className="flex items-center gap-4 mt-4">
                  <span className="text-2xl font-bold text-firefly-red">
                    ${totalPrice.toFixed(2)}
                  </span>
                  <div className="flex items-center text-gray-500 text-sm">
                    <Clock className="w-4 h-4 mr-1" />
                    15-20 min
                  </div>
                  <div className="flex items-center text-gray-500 text-sm">
                    <Users className="w-4 h-4 mr-1" />
                    Serves 1
                  </div>
                </div>
              </div>

              {/* Size Selection */}
              {item.customizations.sizes.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Choose Size</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <RadioGroup value={selectedSize} onValueChange={setSelectedSize}>
                      {item.customizations.sizes.map((size) => (
                        <div key={size.name} className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value={size.name} id={size.name} />
                            <Label htmlFor={size.name}>{size.name}</Label>
                          </div>
                          <span className="text-sm text-gray-600">
                            {size.price > 0 ? `+$${size.price.toFixed(2)}` : 'Free'}
                          </span>
                        </div>
                      ))}
                    </RadioGroup>
                  </CardContent>
                </Card>
              )}

              {/* Add-ons */}
              {item.customizations.addOns.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Add-ons</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    {item.customizations.addOns.map((addOn) => (
                      <div key={addOn.name} className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id={addOn.name}
                            checked={selectedAddOns.includes(addOn.name)}
                            onCheckedChange={(checked) => 
                              handleAddOnChange(addOn.name, checked as boolean)
                            }
                          />
                          <Label htmlFor={addOn.name}>{addOn.name}</Label>
                        </div>
                        <span className="text-sm text-gray-600">
                          +${addOn.price.toFixed(2)}
                        </span>
                      </div>
                    ))}
                  </CardContent>
                </Card>
              )}

              {/* Quantity and Add to Cart */}
              <Card>
                <CardContent className="pt-6">
                  <div className="flex items-center justify-between mb-4">
                    <span className="text-lg font-medium">Quantity</span>
                    <div className="flex items-center gap-3">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setQuantity(Math.max(1, quantity - 1))}
                        disabled={quantity <= 1}
                      >
                        <Minus className="w-4 h-4" />
                      </Button>
                      <span className="text-lg font-medium min-w-[2rem] text-center">
                        {quantity}
                      </span>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setQuantity(quantity + 1)}
                      >
                        <Plus className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                  
                  <Button
                    onClick={handleAddToCart}
                    className="w-full bg-firefly-red hover:bg-red-700 text-white"
                    size="lg"
                  >
                    Add to Cart - ${totalPrice.toFixed(2)}
                  </Button>
                </CardContent>
              </Card>

              {/* Nutritional Info */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Nutritional Information</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="font-medium">Calories:</span> {item.nutritionalInfo.calories}
                    </div>
                    <div>
                      <span className="font-medium">Protein:</span> {item.nutritionalInfo.protein}g
                    </div>
                    <div>
                      <span className="font-medium">Carbs:</span> {item.nutritionalInfo.carbs}g
                    </div>
                    <div>
                      <span className="font-medium">Fat:</span> {item.nutritionalInfo.fat}g
                    </div>
                  </div>
                  
                  <Separator className="my-4" />
                  
                  <div>
                    <h4 className="font-medium mb-2">Ingredients:</h4>
                    <p className="text-sm text-gray-600">{item.ingredients.join(', ')}</p>
                  </div>
                  
                  {item.allergens.length > 0 && (
                    <>
                      <Separator className="my-4" />
                      <div>
                        <h4 className="font-medium mb-2">Allergens:</h4>
                        <div className="flex flex-wrap gap-2">
                          {item.allergens.map((allergen) => (
                            <Badge key={allergen} variant="outline" className="text-xs">
                              {allergen}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default ItemDetail;
