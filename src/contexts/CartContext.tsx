import React, { createContext, useContext, useEffect, useState } from 'react';
import { CartItem, getCart, setCart, addToCart as addToCartStorage, removeFromCart as removeFromCartStorage, updateCartItemQuantity as updateCartItemQuantityStorage, clearCart as clearCartStorage, getCartTotal, getCartItemCount } from '@/lib/localStorage';

interface CartContextType {
  cart: CartItem[];
  cartTotal: number;
  cartItemCount: number;
  isCartOpen: boolean;
  addToCart: (item: CartItem) => void;
  removeFromCart: (itemId: string) => void;
  updateCartItemQuantity: (itemId: string, quantity: number) => void;
  clearCart: () => void;
  openCart: () => void;
  closeCart: () => void;
  toggleCart: () => void;
}

const CartContext = createContext<CartContextType | undefined>(undefined);

export const useCart = () => {
  const context = useContext(CartContext);
  if (context === undefined) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
};

interface CartProviderProps {
  children: React.ReactNode;
}

export const CartProvider: React.FC<CartProviderProps> = ({ children }) => {
  const [cart, setCartState] = useState<CartItem[]>([]);
  const [cartTotal, setCartTotal] = useState(0);
  const [cartItemCount, setCartItemCount] = useState(0);
  const [isCartOpen, setIsCartOpen] = useState(false);

  // Load cart from localStorage on mount
  useEffect(() => {
    const savedCart = getCart();
    setCartState(savedCart);
    updateCartStats(savedCart);
  }, []);

  // Update cart statistics
  const updateCartStats = (cartItems: CartItem[]) => {
    const total = cartItems.reduce((sum, item) => sum + item.totalPrice, 0);
    const count = cartItems.reduce((sum, item) => sum + item.quantity, 0);
    setCartTotal(total);
    setCartItemCount(count);
  };

  const addToCart = (item: CartItem) => {
    addToCartStorage(item);
    const updatedCart = getCart();
    setCartState(updatedCart);
    updateCartStats(updatedCart);
    setIsCartOpen(true); // Open cart when item is added
  };

  const removeFromCart = (itemId: string) => {
    removeFromCartStorage(itemId);
    const updatedCart = getCart();
    setCartState(updatedCart);
    updateCartStats(updatedCart);
  };

  const updateCartItemQuantity = (itemId: string, quantity: number) => {
    updateCartItemQuantityStorage(itemId, quantity);
    const updatedCart = getCart();
    setCartState(updatedCart);
    updateCartStats(updatedCart);
  };

  const clearCart = () => {
    clearCartStorage();
    setCartState([]);
    updateCartStats([]);
  };

  const openCart = () => setIsCartOpen(true);
  const closeCart = () => setIsCartOpen(false);
  const toggleCart = () => setIsCartOpen(!isCartOpen);

  const value: CartContextType = {
    cart,
    cartTotal,
    cartItemCount,
    isCartOpen,
    addToCart,
    removeFromCart,
    updateCartItemQuantity,
    clearCart,
    openCart,
    closeCart,
    toggleCart,
  };

  return (
    <CartContext.Provider value={value}>
      {children}
    </CartContext.Provider>
  );
};
