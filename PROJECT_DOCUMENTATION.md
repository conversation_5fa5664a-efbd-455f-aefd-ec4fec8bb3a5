# FireFly Burger Web Application

## 🍔 Project Overview

# FireFly Burger Web Application

## 🍔 Project Overview

FireFly Burger is a comprehensive digital ecosystem consisting of three main applications:

1. **Customer Website** - A modern, responsive ordering platform for customers to browse menus, place orders, manage addresses, and find restaurant locations.

2. **Customer Mobile App** - A native mobile application with all customer website features optimized for mobile devices, including push notifications, offline support, and mobile-specific features.

3. **Corporate Website** - A professional franchise-focused platform for potential franchisees, featuring company information, franchise opportunities, and application processes.

Built with modern technologies including React, TypeScript, Tailwind CSS for web platforms and React Native for mobile, all applications feature stunning designs and clean, modern interfaces that embody the brand's bold and fiery personality.

**Live Demo:** [FireFly Burger Website](https://firefly-burger-web.vercel.app)

## 🎯 Project Goals

### 🍔 Customer Website & Mobile App
Create a modern, intuitive ordering platform that enables customers to:
- Browse comprehensive menu with real-time pricing
- Place orders with seamless checkout experience
- Subscribe to premium plans (<PERSON><PERSON><PERSON> & Fluffy) with exclusive benefits
- Earn and redeem reward points through integrated loyalty system
- Manage delivery addresses and preferences
- Switch between international branches with localized content
- Track orders and access customer support
- **Mobile App Exclusive Features:**
  - Push notifications for order updates and promotions
  - Offline menu browsing and order preparation
  - Biometric authentication (Face ID, Touch ID)
  - Location-based branch suggestions and auto-switching
  - Mobile wallet integration (Apple Pay, Google Pay)
  - Camera-based QR code scanning for quick reorders

### 🏢 Corporate Website
Develop a professional franchise platform that helps potential franchisees:
- Explore investment opportunities and ROI projections
- Access comprehensive franchise information and requirements
- Complete franchise applications through guided wizard
- Connect with existing franchisees and success stories
- Access territory availability and market analysis

### 🌍 Global Expansion
Build international capabilities to support worldwide growth:
- Multi-country branch management and selection
- Real-time currency conversion and localized pricing
- Multi-language support and cultural localization
- International payment processing and compliance
- Branch-specific promotions and market adaptations

### ⚙️ Technical Infrastructure
Establish robust, scalable architecture that provides:
- High-performance multi-platform system (Web + Mobile + Corporate)
- Firebase Backend-as-a-Service for scalable, real-time operations
- Responsive web design and native mobile applications
- Advanced state management for complex user interactions
- Comprehensive analytics and reporting capabilities
- SEO optimization and accessibility compliance
- Cross-platform code sharing and unified backend services

## 🚀 Tech Stack

### Core Technologies
**Web Platforms:**
- **Frontend Framework:** React 18.3.1
- **Build Tool:** Vite 5.4.1
- **Language:** TypeScript 5.5.3
- **Styling:** Tailwind CSS 3.4.11
- **UI Components:** shadcn/ui (Radix UI primitives)
- **Routing:** React Router DOM 6.26.2
- **State Management:** TanStack React Query 5.56.2

**Mobile Application:**
- **Framework:** React Native 0.73.x
- **Language:** TypeScript 5.5.3
- **Navigation:** React Navigation 6.x
- **State Management:** Redux Toolkit + RTK Query
- **UI Components:** React Native Elements + Custom Components
- **Styling:** StyleSheet + React Native Reanimated

### Key Dependencies
**Shared Services:**
- **Backend Services:** Firebase 10.x (Authentication, Firestore, Storage, Functions)
- **Payment Processing:** Stripe SDK for subscription and order payments

**Web Dependencies:**
- **Carousel:** Embla Carousel React 8.3.0
- **Forms:** React Hook Form 7.53.0 + Zod 3.23.8
- **Icons:** Lucide React 0.462.0
- **Charts:** Recharts 2.12.7
- **Animations:** Tailwind CSS Animate
- **Notifications:** Sonner 1.5.0

**Mobile Dependencies:**
- **Navigation:** @react-navigation/native 6.x
- **Icons:** React Native Vector Icons
- **Animations:** React Native Reanimated 3.x
- **Push Notifications:** @react-native-firebase/messaging
- **Biometric Auth:** React Native Biometrics
- **Camera:** React Native Camera / Expo Camera
- **Maps:** React Native Maps
- **Payments:** @stripe/stripe-react-native

## 📁 Project Structure

```
src/                     # Web Application
├── components/           # Reusable UI components
│   ├── ui/              # shadcn/ui components
│   ├── customer/        # Customer website components
│   │   ├── BurgerCategories.tsx
│   │   ├── FeaturedBurgers.tsx
│   │   ├── HeroSection.tsx
│   │   ├── SearchSection.tsx
│   │   ├── BranchSelector.tsx
│   │   ├── SubscriptionPlans.tsx
│   │   ├── RewardsSection.tsx
│   │   └── Footer.tsx
│   ├── corporate/       # Corporate website components
│   │   ├── CorporateHero.tsx
│   │   ├── FranchiseOpportunities.tsx
│   │   ├── CompanyStats.tsx
│   │   ├── TestimonialsSection.tsx
│   │   ├── FranchiseApplicationCTA.tsx
│   │   └── CorporateFooter.tsx
│   └── shared/          # Shared components
│       └── Navbar.tsx
├── pages/               # Page components
│   ├── customer/        # Customer website pages
│   │   ├── Index.tsx    # Customer landing page
│   │   ├── ItemDetail.tsx
│   │   ├── Checkout.tsx
│   │   ├── FastCheckout.tsx
│   │   ├── Addresses.tsx
│   │   ├── Branches.tsx
│   │   ├── BranchSelector.tsx
│   │   ├── Subscription.tsx
│   │   ├── SubscriptionPlans.tsx
│   │   ├── ManageSubscription.tsx
│   │   └── Rewards.tsx
│   ├── corporate/       # Corporate website pages
│   │   ├── Corporate.tsx # Corporate landing page
│   │   ├── About.tsx
│   │   ├── Franchise.tsx
│   │   ├── FranchiseApplication.tsx
│   │   ├── Investors.tsx
│   │   ├── Careers.tsx
│   │   └── Press.tsx
│   ├── auth/           # Authentication pages
│   └── NotFound.tsx    # 404 page
├── lib/                # Utility functions
├── hooks/              # Custom React hooks
└── index.css          # Global styles

mobile/                  # React Native Mobile App
├── src/
│   ├── components/      # Mobile UI components
│   │   ├── common/      # Shared mobile components
│   │   ├── screens/     # Screen-specific components
│   │   └── navigation/  # Navigation components
│   ├── screens/         # Mobile app screens
│   │   ├── auth/        # Authentication screens
│   │   ├── menu/        # Menu and ordering screens
│   │   ├── profile/     # User profile and settings
│   │   ├── subscription/# Subscription management
│   │   ├── rewards/     # Rewards and loyalty
│   │   └── branches/    # Branch selection and finder
│   ├── navigation/      # Navigation configuration
│   ├── services/        # API and Firebase services
│   ├── store/          # Redux store and slices
│   ├── utils/          # Utility functions
│   └── types/          # TypeScript type definitions
├── android/            # Android-specific code
├── ios/               # iOS-specific code
└── package.json       # Mobile dependencies
```

## 🎨 Design System

### Brand Colors
```css
firefly-red: #f8371a      /* Primary brand color */
firefly-black: #121212    /* Dark backgrounds */
firefly-darkgray: #222222 /* Secondary dark */
firefly-lightgray: #f5f5f5 /* Light backgrounds */
firefly-cream: #FFF8E1    /* Accent color */
firefly-gold: #FFD700     /* Premium/corporate accent */
```

### Typography
- **Primary Font:** Montserrat (Google Fonts)
- **Hero Font:** NexaRustSlab (Custom font for headings)
- **Corporate Font:** Inter (Professional corporate content)
- **Font Weights:** 400, 500, 600, 700, 800

### Custom Animations
- **bounce-in:** Card entrance animations
- **float:** Floating burger image effect
- **pulse-glow:** Button glow effects
- **slide-up:** Corporate section reveals
- **fade-in-scale:** Franchise CTA animations

## 🏗️ Architecture

### Routing Structure
```
/ (Customer Landing Page)
├── /items/:id (Item detailed page)
├── /checkout (Checkout page)
├── /fast-checkout (Fast checkout page)
├── /addresses (Customer addresses management)
├── /branches (Find branch locations)
├── /branch-selector (International branch selection)
├── /subscription (Subscription plans overview)
├── /subscription/juicy (Juicy plan details)
├── /subscription/fluffy (Fluffy plan details)
├── /subscription/manage (Manage current subscription)
├── /rewards (Reward points and redemption)
├── /corporate (Corporate landing page)
├── /corporate/about (Company information)
├── /corporate/franchise (Franchise opportunities)
├── /corporate/franchise/apply (Franchise Application Wizard)
├── /corporate/investors (Investor relations)
├── /corporate/careers (Career opportunities)
├── /corporate/press (Press releases and media)
├── /auth/
│   ├── /signin
│   ├── /signup
│   └── /reset
└── /dashboard/
    ├── / (Overview)
    ├── /menu (Menu management)
    ├── /orders (Order management)
    ├── /analytics (Analytics dashboard)
    └── /settings (User settings)
```

### Firebase Backend Infrastructure

#### Why Firebase is the Optimal Choice

**🚀 Scalability & Performance**
- **Auto-scaling:** Handles traffic spikes during peak ordering times
- **Global CDN:** Fast content delivery worldwide for international branches
- **Real-time Database:** Instant updates for orders, inventory, and pricing
- **Offline Support:** Seamless experience even with poor connectivity

**💰 Cost Efficiency**
- **Pay-as-you-scale:** No upfront infrastructure costs
- **Serverless Functions:** Only pay for actual usage
- **Integrated Services:** Reduces need for multiple third-party services
- **Reduced DevOps:** Minimal server management and maintenance

**🔒 Security & Compliance**
- **Enterprise-grade Security:** Google's security infrastructure
- **Built-in Authentication:** Secure user management with social logins
- **Data Encryption:** End-to-end encryption for sensitive data
- **GDPR Compliance:** Built-in privacy and data protection features

**⚡ Development Speed**
- **Rapid Prototyping:** Quick feature development and deployment
- **Real-time Features:** Live order tracking and notifications
- **Easy Integration:** Seamless React integration with Firebase SDK
- **Rich Documentation:** Extensive guides and community support

#### Firebase Services Implementation

**🔐 Firebase Authentication**
- Customer and franchisee user management
- Social login integration (Google, Facebook, Apple)
- Multi-factor authentication for corporate users
- Role-based access control (customers, franchisees, admins)

**📊 Cloud Firestore Database**
- **Menu Management:** Real-time menu updates across all branches
- **Order Processing:** Order tracking and status management
- **User Profiles:** Customer preferences and subscription data
- **Branch Data:** International branch information and availability
- **Subscription Management:** Juicy & Fluffy plan data and billing
- **Rewards System:** Points tracking and redemption history

**☁️ Cloud Functions**
- **Order Processing:** Automated order workflow and notifications
- **Payment Processing:** Stripe integration for subscriptions and orders
- **Rewards Calculation:** Automatic points calculation and tier updates
- **Email Notifications:** Order confirmations and subscription updates
- **Data Synchronization:** Branch-specific pricing and menu updates

**📁 Cloud Storage**
- **Menu Images:** High-quality product photography
- **User Uploads:** Profile pictures and franchise application documents
- **Marketing Assets:** Promotional images and corporate materials
- **Branch Media:** Location photos and promotional content

**📈 Firebase Analytics**
- **User Behavior:** Customer journey and conversion tracking
- **Performance Monitoring:** App performance and crash reporting
- **A/B Testing:** Feature testing and optimization
- **Custom Events:** Subscription conversions and reward redemptions

**🔔 Cloud Messaging (FCM)**
- **Order Updates:** Real-time order status notifications
- **Promotional Offers:** Targeted marketing for subscription plans
- **Franchise Communications:** Application status and updates
- **Reward Notifications:** Points earned and redemption opportunities

### Component Architecture
- **Atomic Design:** Components built with reusability in mind
- **Domain Separation:** Clear separation between customer and corporate components
- **Responsive Design:** Mobile-first approach with Tailwind breakpoints
- **Accessibility:** ARIA labels and keyboard navigation support
- **Performance:** Lazy loading and optimized animations
- **Firebase Integration:** Real-time data binding and offline support

## 🌟 Key Features

### Customer Website
1. **Hero Section**
   - Bold typography with custom NexaRustSlab font
   - Floating burger animation
   - Responsive navigation menu
   - International branch selector in header

2. **Search Section**
   - Clean search interface
   - Call-to-action messaging
   - Real-time menu item search

3. **Categories Section**
   - Horizontal slider with navigation controls
   - White cards with subtle shadows
   - 6 food categories with images and descriptions
   - Smooth animations and hover effects

4. **Trending Items**
   - 4-column responsive grid layout
   - Compact white cards with product images
   - Price display and "Add to Cart" functionality
   - Popular and Spicy badges
   - Dynamic trending algorithm

5. **Item Detailed Page**
   - Comprehensive product information
   - High-quality product images
   - Nutritional information and ingredients
   - Customization options and add-ons
   - Customer reviews and ratings

6. **Checkout System**
   - **Standard Checkout:** Full order review and customization
   - **Fast Checkout:** One-click ordering for returning customers
   - Multiple payment methods support
   - Order tracking and confirmation

7. **Customer Addresses**
   - Multiple delivery address management
   - Address validation and geocoding
   - Default address settings
   - Quick address selection during checkout

8. **Find Branch Page**
   - Interactive map with all restaurant locations
   - Branch details (hours, contact, services)
   - Distance calculation and directions
   - Filter by services (dine-in, takeaway, delivery)

9. **International Branch Selector**
   - Country and branch selection dropdown
   - Real-time menu and pricing updates
   - Currency conversion and localization
   - Geolocation-based branch suggestions
   - Branch availability and service hours
   - Delivery zone validation for selected branch

10. **Subscription Plans (Juicy & Fluffy)**
    - **Juicy Plan:** Premium subscription with enhanced benefits
      - Exclusive menu items and early access to new products
      - 2x reward points on all orders
      - Free delivery on orders over $15
      - Monthly surprise treats and discounts
      - Priority customer support
    - **Fluffy Plan:** Ultimate subscription with maximum benefits
      - All Juicy plan benefits included
      - 3x reward points on all orders
      - Free delivery on all orders (no minimum)
      - Weekly exclusive offers and flash sales
      - VIP access to special events and tastings
      - Personalized meal recommendations
    - Subscription management and billing
    - Plan comparison and upgrade options

11. **Rewards System**
    - Earn points on every purchase
    - Redeem points for free items and discounts
    - Tier-based rewards (Bronze, Silver, Gold, Platinum)
    - Birthday rewards and anniversary bonuses
    - Referral program with bonus points
    - Integration with subscription plans for multiplied earnings

12. **Footer Section**
    - Download app links (iOS and Android)
    - Social media links
    - Quick navigation menu
    - Contact information and support
    - Current branch and country indicator
    - Subscription status and rewards balance

### Customer Mobile App (iOS & Android)
1. **Native Mobile Experience**
   - **Optimized Performance:** Native rendering for smooth interactions
   - **Platform Integration:** iOS and Android design guidelines compliance
   - **Offline Functionality:** Browse menus and prepare orders without internet
   - **Background Sync:** Automatic data synchronization when online

2. **Enhanced Authentication**
   - **Biometric Login:** Face ID, Touch ID, and fingerprint authentication
   - **Social Login:** Google, Facebook, Apple Sign-In integration
   - **Secure Storage:** Encrypted local storage for sensitive data
   - **Auto-login:** Seamless authentication with stored credentials

3. **Push Notifications**
   - **Order Updates:** Real-time order status and delivery notifications
   - **Promotional Offers:** Targeted marketing for subscription plans and deals
   - **Reward Alerts:** Points earned, tier upgrades, and redemption opportunities
   - **Branch Updates:** New menu items and location-specific promotions

4. **Location-Based Features**
   - **Auto Branch Detection:** Automatic branch switching based on GPS location
   - **Delivery Tracking:** Real-time delivery tracking with map integration
   - **Nearby Branches:** Find closest locations with directions and details
   - **Geofencing:** Location-triggered notifications and offers

5. **Mobile-Optimized Ordering**
   - **Quick Reorder:** One-tap reordering of favorite items
   - **QR Code Scanning:** Scan codes for instant menu access and promotions
   - **Voice Search:** Voice-activated menu item search
   - **Shake to Clear:** Gesture-based cart clearing and app refresh

6. **Mobile Wallet Integration**
   - **Apple Pay:** Seamless iOS payment integration
   - **Google Pay:** Android payment wallet support
   - **Stored Cards:** Secure card storage with tokenization
   - **Subscription Billing:** Mobile-optimized subscription management

7. **Camera Features**
   - **QR Code Scanner:** Menu access, promotions, and loyalty point collection
   - **Receipt Scanning:** Digital receipt storage and expense tracking
   - **Food Photography:** Share meals on social media with branded frames
   - **AR Menu Preview:** Augmented reality food visualization (future feature)

8. **Offline Capabilities**
   - **Menu Caching:** Browse full menu without internet connection
   - **Order Preparation:** Build orders offline, sync when connected
   - **Favorites Access:** View saved items and previous orders offline
   - **Branch Information:** Access stored branch details and hours

9. **Mobile-Specific UI/UX**
   - **Swipe Gestures:** Intuitive navigation with swipe actions
   - **Pull-to-Refresh:** Manual data refresh with pull gesture
   - **Haptic Feedback:** Tactile responses for actions and confirmations
   - **Dark Mode:** System-integrated dark theme support

10. **App Store Optimization**
    - **iOS App Store:** Optimized listing with screenshots and descriptions
    - **Google Play Store:** Android marketplace presence with ASO
    - **App Reviews:** In-app review prompts and rating management
    - **Update Management:** Seamless app updates with feature announcements

### Corporate Website
1. **Corporate Hero Section**
   - Professional brand presentation
   - Company mission and vision
   - Key performance indicators
   - Franchise opportunity highlight with prominent CTA

2. **Company Overview**
   - Brand story and heritage
   - Market presence and growth
   - Awards and recognition
   - Leadership team introduction

3. **Franchise Opportunities**
   - Investment requirements and ROI projections
   - Territory availability map
   - Franchise model explanation
   - Success stories and testimonials
   - **Primary CTA:** "Start Your Franchise Journey" button

4. **Franchise Application Wizard**
   - Multi-step application process
   - Personal and financial information collection
   - Territory preference selection
   - Investment capacity assessment
   - Document upload functionality
   - Application status tracking

5. **Company Statistics**
   - Number of locations worldwide
   - Years in business
   - Customer satisfaction metrics
   - Revenue growth charts
   - Market expansion timeline

6. **Franchisee Testimonials**
   - Success stories from existing franchisees
   - Video testimonials and case studies
   - ROI examples and business growth
   - Support system highlights

7. **Investment Information**
   - Detailed franchise costs breakdown
   - Financing options and partnerships
   - Expected ROI and payback period
   - Ongoing fees and royalty structure

8. **Support System**
   - Training programs and duration
   - Marketing support and materials
   - Operations manual and guidelines
   - Ongoing business support services

9. **Corporate Footer**
   - Investor relations links
   - Press and media contacts
   - Legal information and compliance
   - Corporate social responsibility

### Dashboard System
1. **Authentication Pages**
   - Sign in, sign up, and password reset
   - Form validation with React Hook Form + Zod

2. **Menu Management**
   - Add, edit, and delete menu items
   - Category management
   - Image upload support
   - Pricing and description management

3. **Order Management**
   - Order tracking and status updates
   - Customer information display
   - Order analytics

4. **Analytics Dashboard**
   - Sales charts with Recharts
   - Performance metrics
   - Revenue tracking

## 🎯 Recent Updates (Landing Page Optimization)

### Categories Section Improvements
- ✅ **White Theme:** Changed from red gradient to clean white background
- ✅ **Compact Design:** Reduced card size from 450px to 350px height
- ✅ **Horizontal Slider:** Replaced grid with carousel navigation
- ✅ **Subtle Shadows:** Added professional depth with hover effects
- ✅ **Removed 3D Effects:** Clean, flat design approach
- ✅ **Enhanced Spacing:** Optimized padding and margins
- ✅ **Simplified Interaction:** Removed click functionality for cleaner UX

### Trending Items Optimization
- ✅ **4-Column Layout:** Optimized for desktop viewing
- ✅ **White Theme:** Consistent with categories section
- ✅ **Compact Cards:** Reduced size for better fit (450px → 350px)
- ✅ **Better Spacing:** Increased gap between cards
- ✅ **Typography Scaling:** Proportional text sizing for smaller cards
- ✅ **Component Optimization:** Smaller buttons and badges
- ✅ **Dynamic Content:** Shows trending items based on popularity

## 🚧 Upcoming Features

### Customer Experience Enhancements
1. **Item Detailed Page** (`/items/:id`)
   - Product image gallery with zoom functionality
   - Detailed descriptions and nutritional information
   - Customization options (size, toppings, modifications)
   - Customer reviews and ratings system
   - Related items suggestions
   - Add to cart with quantity selection

2. **Enhanced Checkout System**
   - **Standard Checkout** (`/checkout`)
     - Order review and modification
     - Delivery/pickup options
     - Payment method selection
     - Special instructions
   - **Fast Checkout** (`/fast-checkout`)
     - One-click ordering for saved preferences
     - Stored payment methods
     - Default delivery address

3. **Customer Address Management** (`/addresses`)
   - Add, edit, and delete delivery addresses
   - Set default delivery location
   - Address validation and autocomplete
   - Delivery zone verification

4. **Branch Locator** (`/branches`)
   - Interactive map with all restaurant locations
   - Branch information (hours, contact, services)
   - Distance calculation and directions
   - Filter by available services

5. **International Branch System** (`/branch-selector`)
   - Country selection with flag icons
   - Branch listing with details and services
   - Real-time menu synchronization
   - Currency conversion and pricing updates
   - Localized content and language support
   - Branch-specific promotions and offers

6. **Subscription Management System** (`/subscription/*`)
   - **Plan Overview Page** (`/subscription`)
     - Juicy and Fluffy plan comparison
     - Benefits breakdown and pricing
     - Customer testimonials and success stories
   - **Plan Details Pages** (`/subscription/juicy`, `/subscription/fluffy`)
     - Detailed feature lists and benefits
     - Pricing tiers and billing options
     - Sign-up flow with payment integration
   - **Subscription Management** (`/subscription/manage`)
     - Current plan status and usage
     - Billing history and payment methods
     - Plan upgrade/downgrade options
     - Subscription pause and cancellation

7. **Rewards and Loyalty System** (`/rewards`)
   - Points balance and earning history
   - Redemption catalog with available rewards
   - Tier progression and benefits
   - Referral program management
   - Special offers and bonus point opportunities

8. **Customer Mobile App Development**
   - **iOS Application** (React Native)
     - Native iOS components and design patterns
     - App Store submission and optimization
     - iOS-specific features (Face ID, Apple Pay, Siri Shortcuts)
     - TestFlight beta testing and distribution
   - **Android Application** (React Native)
     - Material Design implementation
     - Google Play Store submission and ASO
     - Android-specific features (Fingerprint, Google Pay, Widgets)
     - Google Play Console beta testing
   - **Cross-platform Features**
     - Shared business logic and Firebase integration
     - Unified design system with platform adaptations
     - Code sharing between iOS and Android (80%+ shared code)
     - Automated testing and CI/CD pipeline

9. **Enhanced Footer**
   - Mobile app download links (App Store & Google Play)
   - Social media integration
   - Newsletter subscription
   - Customer support links
   - Terms of service and privacy policy
   - Current branch and country display
   - Subscription status and rewards balance

### Corporate Website Development
1. **Franchise Application Wizard** (`/corporate/franchise/apply`)
   - Multi-step form with progress indicator
   - Personal information collection
   - Financial qualification assessment
   - Territory selection with availability map
   - Business plan upload
   - Application review and approval workflow

2. **Interactive Territory Map**
   - Real-time territory availability
   - Market analysis for each region
   - Population and demographic data
   - Competition analysis
   - Revenue potential calculator

3. **Franchisee Portal**
   - Application status tracking
   - Document management
   - Communication with franchise team
   - Training schedule and materials
   - Performance dashboards

4. **Investment Calculator**
   - Dynamic ROI projections
   - Customizable investment scenarios
   - Break-even analysis
   - Financing options comparison

### Technical Improvements
- **Firebase Backend Integration:** Complete backend-as-a-service implementation
  - **Real-time Database:** Firestore for live data synchronization
  - **Authentication:** Firebase Auth with social login integration
  - **Cloud Functions:** Serverless business logic and API endpoints
  - **Cloud Storage:** Scalable file storage for images and documents
  - **Analytics:** Firebase Analytics for user behavior tracking
  - **Messaging:** FCM for push notifications and real-time updates
- **State Management:** React Query + Zustand with Firebase real-time subscriptions
- **Payment Integration:** Stripe integration with Firebase Functions for secure processing
- **Subscription Management:** Automated billing with Firebase Functions and Stripe webhooks
- **Rewards Engine:** Cloud Functions for points calculation and tier management
- **Geolocation:** Firebase-powered address validation and branch finder
- **Internationalization:** Multi-language support with Firebase Remote Config
- **Currency Management:** Real-time exchange rates via Firebase Functions
- **Branch Context:** Global state management with Firebase real-time updates
- **Loyalty Integration:** Subscription and rewards system with Firestore
- **CRM Integration:** Firebase-powered franchise application management
- **Analytics:** Firebase Analytics with custom events for business intelligence
- **SEO Optimization:** Enhanced search engine visibility with Firebase Hosting
- **Performance Monitoring:** Firebase Performance for app optimization
- **Security:** Firebase Security Rules for data protection and access control

## 🛠️ Development

### Getting Started
```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview
```

### Development Server
- **Host:** :: (all interfaces)
- **Port:** 8080
- **Hot Reload:** Enabled with Vite HMR

### Code Quality
- **ESLint:** Configured with TypeScript rules
- **TypeScript:** Strict mode disabled for flexibility
- **Path Aliases:** `@/*` mapped to `./src/*`

## 📱 Responsive Design

### Breakpoints
- **Mobile:** < 640px (1 column layouts)
- **Tablet:** 640px - 1024px (2 column layouts)
- **Desktop:** > 1024px (3-4 column layouts)
- **Large Desktop:** > 1400px (optimized spacing)

### Mobile Optimizations
- Touch-friendly navigation
- Optimized image sizes
- Simplified layouts
- Gesture support for carousels

## 🔧 Configuration Files

### Key Configuration
- **Vite Config:** `vite.config.ts` - Build and dev server setup
- **Tailwind Config:** `tailwind.config.ts` - Custom theme and colors
- **TypeScript Config:** `tsconfig.json` - Compiler options
- **ESLint Config:** `eslint.config.js` - Linting rules
- **shadcn/ui Config:** `components.json` - UI component setup

## 🚀 Deployment

### Firebase Hosting & Backend Deployment

**Frontend Deployment (Firebase Hosting)**
```bash
# Build the application
npm run build

# Deploy to Firebase Hosting
firebase deploy --only hosting

# Deploy with custom domain
firebase deploy --only hosting:production
```

**Backend Deployment (Firebase Functions)**
```bash
# Deploy Cloud Functions
firebase deploy --only functions

# Deploy specific function
firebase deploy --only functions:processOrder

# Deploy database rules and indexes
firebase deploy --only firestore:rules,firestore:indexes
```

**Complete Deployment**
```bash
# Deploy everything (hosting, functions, rules)
firebase deploy
```

### Environment Configuration
- **Development:** Local Firebase emulators for testing
- **Staging:** Firebase staging project for QA testing
- **Production:** Firebase production project with custom domains
- **Multi-region:** Global deployment for international branches

### Firebase Project Structure
```
firebase-project/
├── hosting/          # Frontend build files
├── functions/        # Cloud Functions (Node.js)
├── firestore.rules   # Database security rules
├── firestore.indexes.json  # Database indexes
├── storage.rules     # Storage security rules
└── firebase.json     # Firebase configuration
```

### Advantages of Firebase Deployment
- **Global CDN:** Automatic worldwide content distribution
- **SSL Certificates:** Automatic HTTPS with custom domains
- **Atomic Deployments:** Zero-downtime deployments
- **Rollback Support:** Easy rollback to previous versions
- **Preview Channels:** Test deployments before going live
- **Custom Domains:** Support for multiple domains and subdomains

## 📈 Performance Features

### Optimization Techniques
- **Code Splitting:** Automatic route-based splitting
- **Tree Shaking:** Unused code elimination
- **Image Optimization:** Responsive images with proper sizing
- **CSS Optimization:** Tailwind CSS purging
- **Bundle Analysis:** Vite bundle analyzer integration

### Loading Strategies
- **Intersection Observer:** Scroll-triggered animations
- **Lazy Loading:** Component-level lazy loading
- **Staggered Animations:** Performance-optimized entrance effects

## 🎨 UI/UX Design Principles

### Design Philosophy
- **Clean & Modern:** Minimalist approach with focus on content
- **Brand Consistency:** Firefly red as primary accent color
- **User-Centric:** Intuitive navigation and clear call-to-actions
- **Performance-First:** Smooth animations without compromising speed
- **Professional Corporate:** Sophisticated design for franchise audience

### Accessibility
- **Semantic HTML:** Proper heading hierarchy and landmarks
- **Keyboard Navigation:** Full keyboard accessibility
- **Screen Reader Support:** ARIA labels and descriptions
- **Color Contrast:** WCAG compliant color combinations

## 📝 Contributing

### Development Workflow
1. Create feature branch from `main`
2. Implement changes with proper TypeScript types
3. Test responsive design across breakpoints
4. Ensure accessibility compliance
5. Update documentation if needed
6. Submit pull request with detailed description

### Code Standards
- **TypeScript:** Use proper typing for all components
- **Components:** Follow atomic design principles
- **Styling:** Use Tailwind classes, avoid custom CSS when possible
- **Testing:** Write tests for critical functionality
- **Documentation:** Comment complex logic and component props

---

**Built with ❤️ by the FireFly Burger Team**

**Live Demo:** [FireFly Burger Website](https://firefly-burger-web.vercel.app)

## 🚀 Tech Stack

### Core Technologies
- **Frontend Framework:** React 18.3.1
- **Build Tool:** Vite 5.4.1
- **Language:** TypeScript 5.5.3
- **Styling:** Tailwind CSS 3.4.11
- **UI Components:** shadcn/ui (Radix UI primitives)
- **Routing:** React Router DOM 6.26.2
- **State Management:** TanStack React Query 5.56.2

### Key Dependencies
- **Carousel:** Embla Carousel React 8.3.0
- **Forms:** React Hook Form 7.53.0 + Zod 3.23.8
- **Icons:** Lucide React 0.462.0
- **Charts:** Recharts 2.12.7
- **Animations:** Tailwind CSS Animate
- **Notifications:** Sonner 1.5.0

## 📁 Project Structure

```
src/
├── components/           # Reusable UI components
│   ├── ui/              # shadcn/ui components
│   ├── BurgerCategories.tsx
│   ├── FeaturedBurgers.tsx
│   ├── HeroSection.tsx
│   ├── Navbar.tsx
│   ├── SearchSection.tsx
│   ├── InstagramFeed.tsx
│   └── Footer.tsx
├── pages/               # Page components
│   ├── auth/           # Authentication pages
│   ├── dashboard/      # Dashboard pages
│   ├── Index.tsx       # Landing page
│   ├── Corporate.tsx   # Corporate page
│   └── NotFound.tsx    # 404 page
├── lib/                # Utility functions
├── hooks/              # Custom React hooks
└── index.css          # Global styles
```

## 🎨 Design System

### Brand Colors
```css
firefly-red: #f8371a      /* Primary brand color */
firefly-black: #121212    /* Dark backgrounds */
firefly-darkgray: #222222 /* Secondary dark */
firefly-lightgray: #f5f5f5 /* Light backgrounds */
firefly-cream: #FFF8E1    /* Accent color */
```

### Typography
- **Primary Font:** Montserrat (Google Fonts)
- **Hero Font:** NexaRustSlab (Custom font for headings)
- **Font Weights:** 400, 500, 600, 700, 800

### Custom Animations
- **bounce-in:** Card entrance animations
- **float:** Floating burger image effect
- **pulse-glow:** Button glow effects

## 🏗️ Architecture

### Routing Structure
```
/ (Landing Page)
├── /corporate (Company information)
├── /items/:id (Item detailed page)
├── /checkout (Checkout page)
├── /fast-checkout (Fast checkout page)
├── /addresses (Customer addresses management)
├── /branches (Find branch locations)
├── /auth/
│   ├── /signin
│   ├── /signup
│   └── /reset
└── /dashboard/
    ├── / (Overview)
    ├── /menu (Menu management)
    ├── /menu/new (Add menu item)
    ├── /menu/categories (Category management)
    ├── /orders (Order management)
    ├── /analytics (Analytics dashboard)
    └── /settings (User settings)
```

### Component Architecture
- **Atomic Design:** Components built with reusability in mind
- **Responsive Design:** Mobile-first approach with Tailwind breakpoints
- **Accessibility:** ARIA labels and keyboard navigation support
- **Performance:** Lazy loading and optimized animations

## 🌟 Key Features

### Customer Website
1. **Hero Section**
   - Bold typography with custom NexaRustSlab font
   - Floating burger animation
   - Responsive navigation menu

2. **Search Section**
   - Clean search interface
   - Call-to-action messaging
   - Real-time menu item search

3. **Categories Section**
   - Horizontal slider with navigation controls
   - White cards with subtle shadows
   - 6 food categories with images and descriptions
   - Smooth animations and hover effects

4. **Trending Items**
   - 4-column responsive grid layout
   - Compact white cards with product images
   - Price display and "Add to Cart" functionality
   - Popular and Spicy badges
   - Dynamic trending algorithm

5. **Item Detailed Page**
   - Comprehensive product information
   - High-quality product images
   - Nutritional information and ingredients
   - Customization options and add-ons
   - Customer reviews and ratings

6. **Checkout System**
   - **Standard Checkout:** Full order review and customization
   - **Fast Checkout:** One-click ordering for returning customers
   - Multiple payment methods support
   - Order tracking and confirmation

7. **Customer Addresses**
   - Multiple delivery address management
   - Address validation and geocoding
   - Default address settings
   - Quick address selection during checkout

8. **Find Branch Page**
   - Interactive map with all restaurant locations
   - Branch details (hours, contact, services)
   - Distance calculation and directions
   - Filter by services (dine-in, takeaway, delivery)

9. **Footer Section**
   - Download app links (iOS and Android)
   - Social media links
   - Quick navigation menu
   - Contact information and support

### Dashboard System
1. **Authentication Pages**
   - Sign in, sign up, and password reset
   - Form validation with React Hook Form + Zod

2. **Menu Management**
   - Add, edit, and delete menu items
   - Category management
   - Image upload support
   - Pricing and description management

3. **Order Management**
   - Order tracking and status updates
   - Customer information display
   - Order analytics

4. **Analytics Dashboard**
   - Sales charts with Recharts
   - Performance metrics
   - Revenue tracking

## 🎯 Recent Updates (Landing Page Optimization)

### Categories Section Improvements
- ✅ **White Theme:** Changed from red gradient to clean white background
- ✅ **Compact Design:** Reduced card size from 450px to 350px height
- ✅ **Horizontal Slider:** Replaced grid with carousel navigation
- ✅ **Subtle Shadows:** Added professional depth with hover effects
- ✅ **Removed 3D Effects:** Clean, flat design approach
- ✅ **Enhanced Spacing:** Optimized padding and margins
- ✅ **Simplified Interaction:** Removed click functionality for cleaner UX

### Trending Items Optimization
- ✅ **4-Column Layout:** Optimized for desktop viewing
- ✅ **White Theme:** Consistent with categories section
- ✅ **Compact Cards:** Reduced size for better fit (450px → 350px)
- ✅ **Better Spacing:** Increased gap between cards
- ✅ **Typography Scaling:** Proportional text sizing for smaller cards
- ✅ **Component Optimization:** Smaller buttons and badges
- ✅ **Dynamic Content:** Shows trending items based on popularity

## � Upcoming Features

### Customer Experience Enhancements
1. **Item Detailed Page** (`/items/:id`)
   - Product image gallery with zoom functionality
   - Detailed descriptions and nutritional information
   - Customization options (size, toppings, modifications)
   - Customer reviews and ratings system
   - Related items suggestions
   - Add to cart with quantity selection

2. **Enhanced Checkout System**
   - **Standard Checkout** (`/checkout`)
     - Order review and modification
     - Delivery/pickup options
     - Payment method selection
     - Special instructions
   - **Fast Checkout** (`/fast-checkout`)
     - One-click ordering for saved preferences
     - Stored payment methods
     - Default delivery address

3. **Customer Address Management** (`/addresses`)
   - Add, edit, and delete delivery addresses
   - Set default delivery location
   - Address validation and autocomplete
   - Delivery zone verification

4. **Branch Locator** (`/branches`)
   - Interactive map with all restaurant locations
   - Branch information (hours, contact, services)
   - Distance calculation and directions
   - Filter by available services

5. **Enhanced Footer**
   - Mobile app download links (App Store & Google Play)
   - Social media integration
   - Newsletter subscription
   - Customer support links
   - Terms of service and privacy policy

### Technical Improvements
- **State Management:** Implement cart state management
- **Authentication:** Customer login and profile management
- **Payment Integration:** Stripe/PayPal integration
- **Geolocation:** Address validation and branch finder
- **Push Notifications:** Order status updates
- **Offline Support:** Service worker for offline browsing

## �🛠️ Development

### Getting Started
```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview
```

### Development Server
- **Host:** :: (all interfaces)
- **Port:** 8080
- **Hot Reload:** Enabled with Vite HMR

### Code Quality
- **ESLint:** Configured with TypeScript rules
- **TypeScript:** Strict mode disabled for flexibility
- **Path Aliases:** `@/*` mapped to `./src/*`

## 📱 Responsive Design

### Breakpoints
- **Mobile:** < 640px (1 column layouts)
- **Tablet:** 640px - 1024px (2 column layouts)
- **Desktop:** > 1024px (3-4 column layouts)
- **Large Desktop:** > 1400px (optimized spacing)

### Mobile Optimizations
- Touch-friendly navigation
- Optimized image sizes
- Simplified layouts
- Gesture support for carousels

## 🔧 Configuration Files

### Key Configuration
- **Vite Config:** `vite.config.ts` - Build and dev server setup
- **Tailwind Config:** `tailwind.config.ts` - Custom theme and colors
- **TypeScript Config:** `tsconfig.json` - Compiler options
- **ESLint Config:** `eslint.config.js` - Linting rules
- **shadcn/ui Config:** `components.json` - UI component setup

## 🚀 Deployment

### Build Process
```bash
npm run build
```
Generates optimized static files in `dist/` directory.

### Environment Support
- **Development:** Full debugging and hot reload
- **Production:** Optimized bundles with tree shaking
- **Preview:** Local production preview

## 📈 Performance Features

### Optimization Techniques
- **Code Splitting:** Automatic route-based splitting
- **Tree Shaking:** Unused code elimination
- **Image Optimization:** Responsive images with proper sizing
- **CSS Optimization:** Tailwind CSS purging
- **Bundle Analysis:** Vite bundle analyzer integration

### Loading Strategies
- **Intersection Observer:** Scroll-triggered animations
- **Lazy Loading:** Component-level lazy loading
- **Staggered Animations:** Performance-optimized entrance effects

## 🎨 UI/UX Design Principles

### Design Philosophy
- **Clean & Modern:** Minimalist approach with focus on content
- **Brand Consistency:** Firefly red as primary accent color
- **User-Centric:** Intuitive navigation and clear call-to-actions
- **Performance-First:** Smooth animations without compromising speed

### Accessibility
- **Semantic HTML:** Proper heading hierarchy and landmarks
- **Keyboard Navigation:** Full keyboard accessibility
- **Screen Reader Support:** ARIA labels and descriptions
- **Color Contrast:** WCAG compliant color combinations

## 📝 Contributing

### Development Workflow
1. Create feature branch from `main`
2. Implement changes with proper TypeScript types
3. Test responsive design across breakpoints
4. Ensure accessibility compliance
5. Update documentation if needed
6. Submit pull request with detailed description

### Code Standards
- **TypeScript:** Use proper typing for all components
- **Components:** Follow atomic design principles
- **Styling:** Use Tailwind classes, avoid custom CSS when possible
- **Testing:** Write tests for critical functionality
- **Documentation:** Comment complex logic and component props

---

**Built with ❤️ by the FireFly Burger Team**
